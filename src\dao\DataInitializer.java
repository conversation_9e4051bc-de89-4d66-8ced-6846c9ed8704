package dao;

import entity.Medicine;
import entity.Patient;
import entity.Doctor;
import entity.Consultation;
import entity.Treatment;
import entity.PharmacyTransaction;
import entity.Prescription;
import entity.PrescribedMedicine;
import adt.SetQueueArray;
import adt.SetAndQueueInterface;

public class DataInitializer {
    public static SetAndQueueInterface<Medicine> initializeSampleMedicines() {
        SetAndQueueInterface<Medicine> medicines = new SetQueueArray<>();

        medicines.add(new Medicine("MED001", "Paracetamol", "Panadol", 50, "2025-12-31", 8.50, "Pain Relief", "Paracetamol", "Analgesic"));
        medicines.add(new Medicine("MED002", "Amoxicillin", "Amoxil", 30, "2025-09-30", 15.80, "Antibiotic", "Amoxicillin", "Antibiotic"));
        medicines.add(new Medicine("MED003", "Ibuprofen", "Advil", 25, "2025-10-15", 12.90, "Pain Relief", "Ibuprofen", "NSAID"));
        medicines.add(new Medicine("MED004", "Omeprazole", "Losec", 15, "2025-11-20", 25.60, "Acid Reflux", "Omeprazole", "Proton Pump Inhibitor"));
        medicines.add(new Medicine("MED005", "Cetirizine", "Zyrtec", 5, "2025-12-10", 18.20, "Allergy Relief", "Cetirizine", "Antihistamine"));
        medicines.add(new Medicine("MED006", "Metformin", "Glucophage", 40, "2026-01-28", 22.40, "Diabetes Management", "Metformin", "Biguanide"));
        medicines.add(new Medicine("MED007", "Amlodipine", "Norvasc", 35, "2026-02-15", 32.80, "Hypertension", "Amlodipine", "Calcium Channel Blocker"));
        medicines.add(new Medicine("MED008", "Salbutamol", "Ventolin", 45, "2026-03-10", 28.90, "Asthma Relief", "Salbutamol", "Bronchodilator"));
        medicines.add(new Medicine("MED009", "Sertraline", "Zoloft", 20, "2026-04-05", 45.60, "Depression Treatment", "Sertraline", "SSRI"));
        medicines.add(new Medicine("MED010", "Atorvastatin", "Lipitor", 30, "2026-05-20", 58.90, "Cholesterol Management", "Atorvastatin", "Statin"));
        medicines.add(new Medicine("MED011", "Aspirin", "Bayer", 8, "2026-06-15", 6.50, "Pain Relief", "Acetylsalicylic Acid", "NSAID"));
        medicines.add(new Medicine("MED012", "Insulin", "Humalog", 12, "2026-07-30", 120.00, "Diabetes Management", "Insulin Lispro", "Insulin"));
        medicines.add(new Medicine("MED013", "Loratadine", "Claritin", 18, "2026-08-25", 16.80, "Allergy Relief", "Loratadine", "Antihistamine"));
        medicines.add(new Medicine("MED014", "Lisinopril", "Zestril", 22, "2026-09-10", 28.40, "Hypertension", "Lisinopril", "ACE Inhibitor"));
        medicines.add(new Medicine("MED015", "Fluoxetine", "Prozac", 15, "2026-10-05", 42.30, "Depression Treatment", "Fluoxetine", "SSRI"));
        medicines.add(new Medicine("MED016", "Simvastatin", "Zocor", 25, "2026-11-20", 35.70, "Cholesterol Management", "Simvastatin", "Statin"));
        medicines.add(new Medicine("MED017", "Montelukast", "Singulair", 12, "2026-12-15", 38.90, "Asthma Prevention", "Montelukast", "Leukotriene Receptor Antagonist"));
        medicines.add(new Medicine("MED018", "Pantoprazole", "Protonix", 20, "2027-01-10", 31.20, "Acid Reflux", "Pantoprazole", "Proton Pump Inhibitor"));
        medicines.add(new Medicine("MED019", "Duloxetine", "Cymbalta", 10, "2027-02-05", 67.80, "Depression Treatment", "Duloxetine", "SNRI"));
        medicines.add(new Medicine("MED020", "Losartan", "Cozaar", 28, "2027-03-20", 29.60, "Hypertension", "Losartan", "Angiotensin Receptor Blocker"));
        medicines.add(new Medicine("MED021", "Paracetamol", "Panadol Active", 5, "2025-12-31", 8.50, "Pain Relief", "Paracetamol", "Analgesic"));
        medicines.add(new Medicine("MED022", "Cephalexin", "Keflex", 35, "2026-04-15", 18.90, "Antibiotic", "Cephalexin", "Antibiotic"));
        medicines.add(new Medicine("MED023", "Diclofenac", "Voltaren", 42, "2026-05-20", 14.70, "Pain Relief", "Diclofenac", "NSAID"));
        medicines.add(new Medicine("MED024", "Ranitidine", "Zantac", 38, "2026-06-25", 19.80, "Acid Reflux", "Ranitidine", "H2 Receptor Antagonist"));
        medicines.add(new Medicine("MED025", "Fexofenadine", "Allegra", 28, "2026-07-30", 21.50, "Allergy Relief", "Fexofenadine", "Antihistamine"));
        medicines.add(new Medicine("MED026", "Glipizide", "Glucotrol", 33, "2026-08-10", 26.80, "Diabetes Management", "Glipizide", "Sulfonylurea"));
        medicines.add(new Medicine("MED027", "Nifedipine", "Adalat", 29, "2026-09-15", 35.40, "Hypertension", "Nifedipine", "Calcium Channel Blocker"));
        medicines.add(new Medicine("MED028", "Budesonide", "Pulmicort", 24, "2026-10-20", 52.30, "Asthma Prevention", "Budesonide", "Corticosteroid"));
        medicines.add(new Medicine("MED029", "Escitalopram", "Lexapro", 18, "2026-11-25", 48.90, "Depression Treatment", "Escitalopram", "SSRI"));
        medicines.add(new Medicine("MED030", "Rosuvastatin", "Crestor", 31, "2026-12-30", 62.70, "Cholesterol Management", "Rosuvastatin", "Statin"));
        medicines.add(new Medicine("MED031", "Naproxen", "Aleve", 26, "2027-01-15", 11.40, "Pain Relief", "Naproxen", "NSAID"));
        medicines.add(new Medicine("MED032", "Metoprolol", "Lopressor", 37, "2027-02-20", 24.60, "Hypertension", "Metoprolol", "Beta Blocker"));
        medicines.add(new Medicine("MED033", "Prednisone", "Deltasone", 22, "2027-03-25", 15.80, "Anti-inflammatory", "Prednisone", "Corticosteroid"));
        medicines.add(new Medicine("MED034", "Tramadol", "Ultram", 19, "2027-04-30", 28.70, "Pain Relief", "Tramadol", "Opioid Analgesic"));
        medicines.add(new Medicine("MED035", "Warfarin", "Coumadin", 16, "2027-05-15", 12.30, "Blood Thinner", "Warfarin", "Anticoagulant"));
        medicines.add(new Medicine("MED036", "Levothyroxine", "Synthroid", 41, "2027-06-20", 18.90, "Thyroid Hormone", "Levothyroxine", "Hormone"));
        medicines.add(new Medicine("MED037", "Hydrochlorothiazide", "Microzide", 34, "2027-07-25", 16.50, "Diuretic", "Hydrochlorothiazide", "Thiazide Diuretic"));
        medicines.add(new Medicine("MED038", "Alprazolam", "Xanax", 13, "2027-08-30", 22.80, "Anxiety Treatment", "Alprazolam", "Benzodiazepine"));
        medicines.add(new Medicine("MED039", "Gabapentin", "Neurontin", 27, "2027-09-15", 31.40, "Nerve Pain", "Gabapentin", "Anticonvulsant"));
        medicines.add(new Medicine("MED040", "Clonazepam", "Klonopin", 15, "2027-10-20", 19.60, "Seizure Control", "Clonazepam", "Benzodiazepine"));

        return medicines;
    }

    public static SetAndQueueInterface<Patient> initializeSamplePatients() {
        SetAndQueueInterface<Patient> patients = new SetQueueArray<>();

        patients.add(new Patient(1, "Ahmad bin Abdullah", 3, "Male", "Penicillin", "**********", "123 Jalan Tunku Abdul Rahman, Kuala Lumpur", "01-07-2025", "Fever", "Active"));
        patients.add(new Patient(2, "Siti binti Mohamed", 7, "Female", "Paracetamol", "**********", "456 Jalan Sultan Ismail, Petaling Jaya", "02-07-2025", "Common Cold", "Active"));
        patients.add(new Patient(3, "Raj a/l Kumar", 12, "Male", "Sulfa", "**********", "789 Jalan Bukit Bintang, Kuala Lumpur", "03-07-2025", "Asthma", "Active"));
        patients.add(new Patient(4, "Lim Siew Mei", 15, "Female", "None", "**********", "321 Jalan Ampang, Kuala Lumpur", "04-07-2025", "None", "Active"));
        patients.add(new Patient(5, "Tan Ah Kow", 17, "Male", "Codeine", "**********", "654 Jalan Pudu, Kuala Lumpur", "05-07-2025", "Sports Injury", "Active"));
        patients.add(new Patient(6, "Nurul Huda binti Ismail", 22, "Female", "Latex", "**********", "987 Jalan Cheras, Kuala Lumpur", "06-07-2025", "Migraine", "Active"));
        patients.add(new Patient(7, "Krishnan a/l Muthu", 25, "Male", "None", "**********", "147 Jalan Klang Lama, Kuala Lumpur", "07-07-2025", "Depression", "Active"));
        patients.add(new Patient(8, "Wong Mei Ling", 28, "Female", "Iodine", "**********", "258 Jalan Ipoh, Kuala Lumpur", "08-07-2025", "Thyroid Disorder", "Active"));
        patients.add(new Patient(9, "Mohamed Ali bin Hassan", 31, "Male", "None", "**********", "369 Jalan Gombak, Kuala Lumpur", "09-07-2025", "Anxiety", "Active"));
        patients.add(new Patient(10, "Cheah Siew Fong", 35, "Female", "Shellfish", "**********", "741 Jalan Damansara, Petaling Jaya", "10-07-2025", "None", "Active"));
        patients.add(new Patient(11, "Arun a/l Subramaniam", 38, "Male", "None", "**********", "852 Jalan Bangsar, Kuala Lumpur", "11-07-2025", "High Cholesterol", "Active"));
        patients.add(new Patient(12, "Fatimah binti Omar", 42, "Female", "Peanuts", "**********", "963 Jalan TAR, Kuala Lumpur", "12-07-2025", "None", "Active"));
        patients.add(new Patient(13, "Lee Chong Wei", 45, "Male", "None", "**********", "159 Jalan Imbi, Kuala Lumpur", "13-07-2025", "Sleep Apnea", "Active"));
        patients.add(new Patient(14, "Aisha binti Yusof", 48, "Female", "None", "**********", "357 Jalan Raja Chulan, Kuala Lumpur", "14-07-2025", "None", "Active"));
        patients.add(new Patient(15, "Gan Eng Seng", 52, "Male", "Aspirin", "**********", "486 Jalan Tuanku Abdul Rahman, Kuala Lumpur", "15-07-2025", "Gout", "Active"));
        patients.add(new Patient(16, "Zainab binti Ahmad", 55, "Female", "None", "**********", "753 Jalan Raja Laut, Kuala Lumpur", "16-07-2025", "Hypertension", "Active"));
        patients.add(new Patient(17, "Kumar a/l Rajendran", 58, "Male", "Sulfa", "**********", "951 Jalan Sultan, Kuala Lumpur", "17-07-2025", "Diabetes", "Active"));
        patients.add(new Patient(18, "Chan Mei Lin", 62, "Female", "Latex", "**********", "357 Jalan Pahang, Kuala Lumpur", "18-07-2025", "Asthma", "Active"));
        patients.add(new Patient(19, "Ismail bin Omar", 65, "Male", "None", "**********", "159 Jalan Masjid India, Kuala Lumpur", "19-07-2025", "Heart Disease", "Active"));
        patients.add(new Patient(20, "Priya a/p Ramasamy", 68, "Female", "Penicillin", "**********", "753 Jalan Petaling, Kuala Lumpur", "20-07-2025", "Migraine", "Active"));
        patients.add(new Patient(21, "Ong Teck Seng", 72, "Male", "None", "**********", "951 Jalan Chow Kit, Kuala Lumpur", "21-07-2025", "Depression", "Active"));
        patients.add(new Patient(22, "Noraini binti Zainal", 75, "Female", "Iodine", "**********", "357 Jalan Tun Perak, Kuala Lumpur", "22-07-2025", "Thyroid Disorder", "Active"));
        patients.add(new Patient(23, "Muthu a/l Velu", 78, "Male", "None", "**********", "159 Jalan Dang Wangi, Kuala Lumpur", "23-07-2025", "Anxiety", "Active"));
        patients.add(new Patient(24, "Lau Siew Mei", 82, "Female", "Shellfish", "**********", "753 Jalan Tun Razak, Kuala Lumpur", "24-07-2025", "None", "Active"));
        patients.add(new Patient(25, "Hassan bin Ali", 85, "Male", "None", "**********", "951 Jalan Ampang, Kuala Lumpur", "25-07-2025", "High Cholesterol", "Active"));
        patients.add(new Patient(26, "Aminah binti Rashid", 88, "Female", "Aspirin", "**********", "123 Jalan Sentul, Kuala Lumpur", "26-07-2025", "Arthritis", "Active"));
        patients.add(new Patient(27, "Ravi a/l Shankar", 92, "Male", "None", "**********", "456 Jalan Kepong, Kuala Lumpur", "27-07-2025", "Hypertension", "Active"));
        patients.add(new Patient(28, "Lily Tan Mei Hua", 95, "Female", "Shellfish", "**********", "789 Jalan Setapak, Kuala Lumpur", "28-07-2025", "Allergic Rhinitis", "Active"));
        patients.add(new Patient(29, "Azman bin Yusof", 19, "Male", "Penicillin", "**********", "321 Jalan Wangsa Maju, Kuala Lumpur", "29-07-2025", "Diabetes", "Active"));
        patients.add(new Patient(30, "Grace Lim Soo Cheng", 23, "Female", "None", "**********", "654 Jalan Segambut, Kuala Lumpur", "30-07-2025", "Migraine", "Active"));
        patients.add(new Patient(31, "Suresh a/l Krishnan", 26, "Male", "Iodine", "**********", "987 Jalan Batu Caves, Selangor", "31-07-2025", "Thyroid Disorder", "Active"));
        patients.add(new Patient(32, "Farah binti Kamal", 29, "Female", "Latex", "**********", "147 Jalan Rawang, Selangor", "01-08-2025", "Asthma", "Active"));
        patients.add(new Patient(33, "Danny Ng Wei Ming", 33, "Male", "None", "**********", "258 Jalan Kajang, Selangor", "02-08-2025", "Sleep Apnea", "Active"));
        patients.add(new Patient(34, "Khadijah binti Hassan", 36, "Female", "Sulfa", "**********", "369 Jalan Selayang, Selangor", "03-08-2025", "Depression", "Active"));
        patients.add(new Patient(35, "Vincent Loh Chee Keong", 39, "Male", "None", "**********", "741 Jalan Subang, Selangor", "04-08-2025", "High Cholesterol", "Active"));
        patients.add(new Patient(36, "Rohani binti Ibrahim", 43, "Female", "Peanuts", "**********", "852 Jalan Shah Alam, Selangor", "05-08-2025", "Heart Disease", "Active"));
        patients.add(new Patient(37, "Prakash a/l Devi", 47, "Male", "None", "**********", "963 Jalan Klang, Selangor", "06-08-2025", "Anxiety", "Active"));
        patients.add(new Patient(38, "Michelle Wong Ai Ling", 51, "Female", "Codeine", "**********", "159 Jalan Puchong, Selangor", "07-08-2025", "Chronic Pain", "Active"));
        patients.add(new Patient(39, "Hafiz bin Rahman", 54, "Male", "None", "**********", "357 Jalan Cyberjaya, Selangor", "08-08-2025", "Diabetes", "Active"));
        patients.add(new Patient(40, "Stephanie Tan Li Ying", 57, "Female", "Shellfish", "**********", "753 Jalan Putrajaya, Putrajaya", "09-08-2025", "Allergic Dermatitis", "Active"));

        return patients;
    }

    public static SetAndQueueInterface<Doctor> initializeSampleDoctors() {
        SetAndQueueInterface<Doctor> doctors = new SetQueueArray<>();

        doctors.add(new Doctor("DOC001", "Dr. Sarah Chen Mei Ling", "Cardiology", "**********", "<EMAIL>", true, "Mon-Wed 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC002", "Dr. Robert Kim Ah Kow", "Pediatrics", "**********", "<EMAIL>", true, "Tue-Thu 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC003", "Dr. Lisa Wong Siew Mei", "Neurology", "**********", "<EMAIL>", true, "Wed-Fri 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC004", "Dr. James Lee Chong Wei", "Orthopedics", "**********", "<EMAIL>", false, "Mon-Fri 9AM-5PM", true, "15-07-2025", "20-07-2025"));
        doctors.add(new Doctor("DOC005", "Dr. Maria Garcia binti Abdullah", "Endocrinology", "**********", "<EMAIL>", true, "Mon-Fri 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC006", "Dr. David Wilson a/l Kumar", "Psychiatry", "0123456785", "<EMAIL>", true, "Tue-Sat 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC007", "Dr. Jennifer Brown Mei Fong", "Dermatology", "0123456786", "<EMAIL>", true, "Mon-Thu 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC008", "Dr. Michael Taylor bin Mohamed", "Emergency Medicine", "0123456787", "<EMAIL>", true, "24/7 Shifts", false, "", ""));
        doctors.add(new Doctor("DOC009", "Dr. Amanda Lim Siew Lin", "Oncology", "0123456788", "<EMAIL>", true, "Mon-Fri 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC010", "Dr. Christopher Tan Ah Beng", "Radiology", "**********", "<EMAIL>", true, "Mon-Fri 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC011", "Dr. Emily Wong Mei Ling", "Obstetrics & Gynecology", "**********", "<EMAIL>", true, "Tue-Sat 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC012", "Dr. Benjamin Raj a/l Kumar", "Urology", "**********", "<EMAIL>", true, "Mon-Thu 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC013", "Dr. Rachel Lim Hui Ying", "Internal Medicine", "**********", "<EMAIL>", true, "Mon-Fri 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC014", "Dr. Kevin Tan Wei Jie", "Gastroenterology", "**********", "<EMAIL>", true, "Tue-Sat 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC015", "Dr. Priya Sharma a/p Ravi", "Rheumatology", "**********", "<EMAIL>", true, "Wed-Fri 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC016", "Dr. Ahmad Farid bin Hassan", "Pulmonology", "**********", "<EMAIL>", false, "Mon-Thu 9AM-5PM", true, "10-08-2025", "15-08-2025"));
        doctors.add(new Doctor("DOC017", "Dr. Catherine Ng Siew Lan", "Nephrology", "**********", "<EMAIL>", true, "Mon-Fri 8AM-4PM", false, "", ""));
        doctors.add(new Doctor("DOC018", "Dr. Rajesh Kumar a/l Devi", "Hematology", "**********", "<EMAIL>", true, "Tue-Thu 9AM-5PM", false, "", ""));
        doctors.add(new Doctor("DOC019", "Dr. Melissa Chan Ai Ling", "Infectious Disease", "**********", "<EMAIL>", true, "Wed-Sat 10AM-6PM", false, "", ""));
        doctors.add(new Doctor("DOC020", "Dr. Hafiz Ismail bin Omar", "Anesthesiology", "**********", "<EMAIL>", true, "24/7 On-Call", false, "", ""));

        return doctors;
    }

    public static SetAndQueueInterface<Consultation> initializeSampleConsultations() {
        SetAndQueueInterface<Consultation> consultations = new SetQueueArray<>();

        consultations.add(new Consultation("CON001", "1", "DOC001", "10-07-2025", "Completed", "Patient shows signs of hypertension"));
        consultations.add(new Consultation("CON002", "2", "DOC002", "12-07-2025", "Completed", "Regular checkup"));
        consultations.add(new Consultation("CON003", "3", "DOC003", "15-07-2025", "Completed", "Neurological examination completed"));
        consultations.add(new Consultation("CON004", "4", "DOC005", "16-07-2025", "Completed", "Diabetes management consultation"));
        consultations.add(new Consultation("CON005", "5", "DOC001", "17-07-2025", "Completed", "Cardiac assessment"));
        consultations.add(new Consultation("CON006", "6", "DOC006", "18-07-2025", "Completed", "Mental health evaluation"));
        consultations.add(new Consultation("CON007", "7", "DOC007", "19-07-2025", "Completed", "Skin condition review"));
        consultations.add(new Consultation("CON008", "8", "DOC002", "20-07-2025", "Completed", "Pediatric consultation"));
        consultations.add(new Consultation("CON009", "9", "DOC006", "21-07-2025", "Completed", "Anxiety treatment"));
        consultations.add(new Consultation("CON010", "10", "DOC005", "21-07-2025", "Completed", "Thyroid function test review"));
        consultations.add(new Consultation("CON011", "11", "DOC001", "23-07-2025", "Completed", "Cholesterol management"));
        consultations.add(new Consultation("CON012", "12", "DOC003", "24-07-2025", "Completed", "Neurological examination"));
        consultations.add(new Consultation("CON013", "13", "DOC007", "25-07-2025", "Completed", "Dermatological examination"));
        consultations.add(new Consultation("CON014", "14", "DOC008", "26-07-2025", "Completed", "Emergency consultation"));
        consultations.add(new Consultation("CON015", "15", "DOC009", "27-07-2025", "Completed", "Oncology consultation"));
        consultations.add(new Consultation("CON016", "16", "DOC010", "28-07-2025", "Completed", "Radiological assessment"));
        consultations.add(new Consultation("CON017", "17", "DOC011", "29-07-2025", "Completed", "Gynecological examination"));
        consultations.add(new Consultation("CON018", "18", "DOC012", "30-07-2025", "Completed", "Urological consultation"));
        consultations.add(new Consultation("CON019", "19", "DOC001", "31-07-2025", "Completed", "Cardiac examination"));
        consultations.add(new Consultation("CON020", "20", "DOC006", "01-08-2025", "Completed", "Psychiatric evaluation"));
        consultations.add(new Consultation("CON021", "21", "DOC013", "02-08-2025", "Completed", "Internal medicine consultation"));
        consultations.add(new Consultation("CON022", "22", "DOC014", "03-08-2025", "Completed", "Gastroenterology examination"));
        consultations.add(new Consultation("CON023", "23", "DOC015", "04-08-2025", "Completed", "Rheumatology assessment"));
        consultations.add(new Consultation("CON024", "24", "DOC017", "05-08-2025", "Completed", "Nephrology consultation"));
        consultations.add(new Consultation("CON025", "25", "DOC018", "06-08-2025", "Completed", "Hematology examination"));
        consultations.add(new Consultation("CON026", "26", "DOC019", "07-08-2025", "Completed", "Infectious disease consultation"));
        consultations.add(new Consultation("CON027", "27", "DOC001", "08-08-2025", "Completed", "Follow-up cardiac assessment"));
        consultations.add(new Consultation("CON028", "28", "DOC007", "09-08-2025", "Completed", "Dermatology follow-up"));
        consultations.add(new Consultation("CON029", "29", "DOC005", "10-08-2025", "Completed", "Endocrinology consultation"));
        consultations.add(new Consultation("CON030", "30", "DOC013", "11-08-2025", "Completed", "General health checkup"));
        consultations.add(new Consultation("CON031", "31", "DOC006", "12-08-2025", "Completed", "Mental health follow-up"));
        consultations.add(new Consultation("CON032", "32", "DOC002", "13-08-2025", "Completed", "Pediatric vaccination"));
        consultations.add(new Consultation("CON033", "33", "DOC008", "14-08-2025", "Completed", "Emergency treatment"));
        consultations.add(new Consultation("CON034", "34", "DOC014", "15-08-2025", "Completed", "Digestive system examination"));
        consultations.add(new Consultation("CON035", "35", "DOC015", "16-08-2025", "Completed", "Joint pain assessment"));
        consultations.add(new Consultation("CON036", "36", "DOC001", "17-08-2025", "Completed", "Cardiovascular follow-up examination"));
        consultations.add(new Consultation("CON037", "37", "DOC002", "18-08-2025", "Completed", "Pediatric growth assessment"));
        consultations.add(new Consultation("CON038", "38", "DOC003", "19-08-2025", "Completed", "Neurological screening for headaches"));
        consultations.add(new Consultation("CON039", "39", "DOC004", "20-08-2025", "Completed", "Orthopedic consultation for back pain"));
        consultations.add(new Consultation("CON040", "40", "DOC005", "21-08-2025", "Completed", "Endocrine system evaluation"));
        consultations.add(new Consultation("CON041", "1", "DOC006", "22-08-2025", "Completed", "Mental health follow-up session"));
        consultations.add(new Consultation("CON042", "1", "DOC007", "23-08-2025", "Completed", "Dermatological skin check"));
        consultations.add(new Consultation("CON043", "1", "DOC008", "24-08-2025", "Completed", "Emergency medicine consultation"));
        consultations.add(new Consultation("CON044", "1", "DOC009", "25-08-2025", "Completed", "Oncology screening appointment"));
        consultations.add(new Consultation("CON045", "2", "DOC010", "26-08-2025", "Completed", "Radiological examination review"));
        consultations.add(new Consultation("CON046", "2", "DOC011", "27-08-2025", "Completed", "Gynecological wellness check"));
        consultations.add(new Consultation("CON047", "2", "DOC012", "28-08-2025", "Completed", "Urological assessment"));
        consultations.add(new Consultation("CON048", "2", "DOC013", "29-08-2025", "Completed", "Internal medicine consultation"));
        consultations.add(new Consultation("CON049", "3", "DOC014", "30-08-2025", "Completed", "Gastroenterological examination"));
        consultations.add(new Consultation("CON050", "3", "DOC015", "31-08-2025", "Completed", "Rheumatological joint assessment"));
        consultations.add(new Consultation("CON051", "3", "DOC016", "01-09-2025", "Completed", "Pulmonology breathing test"));
        consultations.add(new Consultation("CON052", "3", "DOC017", "02-09-2025", "Completed", "Nephrology kidney function test"));
        consultations.add(new Consultation("CON053", "4", "DOC018", "03-09-2025", "Completed", "Hematology blood work review"));
        consultations.add(new Consultation("CON054", "4", "DOC019", "04-09-2025", "Completed", "Infectious disease consultation"));
        consultations.add(new Consultation("CON055", "4", "DOC020", "05-09-2025", "Completed", "Anesthesiology pre-operative assessment"));
        consultations.add(new Consultation("CON056", "4", "DOC001", "06-09-2025", "Completed", "Cardiac stress test evaluation"));
        consultations.add(new Consultation("CON057", "3", "DOC002", "07-09-2025", "Completed", "Pediatric immunization consultation"));
        consultations.add(new Consultation("CON058", "8", "DOC003", "08-09-2025", "Completed", "Neurological cognitive assessment"));
        consultations.add(new Consultation("CON059", "15", "DOC005", "09-09-2025", "Completed", "Diabetes management review"));
        consultations.add(new Consultation("CON060", "20", "DOC006", "10-09-2025", "Completed", "Psychiatric medication review"));
        consultations.add(new Consultation("CON061", "6", "DOC007", "11-09-2025", "Completed", "Dermatology mole examination"));
        consultations.add(new Consultation("CON062", "9", "DOC008", "12-09-2025", "Completed", "Emergency trauma assessment"));
        consultations.add(new Consultation("CON063", "14", "DOC009", "13-09-2025", "Completed", "Cancer follow-up consultation"));
        consultations.add(new Consultation("CON064", "16", "DOC010", "14-09-2025", "Completed", "Imaging results discussion"));
        consultations.add(new Consultation("CON065", "7", "DOC011", "15-09-2025", "Completed", "Prenatal care consultation"));
        consultations.add(new Consultation("CON066", "7", "DOC012", "16-09-2025", "Completed", "Prostate examination"));
        consultations.add(new Consultation("CON067", "8", "DOC013", "17-09-2025", "Completed", "General health screening"));
        consultations.add(new Consultation("CON068", "9", "DOC014", "18-09-2025", "Completed", "Digestive health consultation"));
        consultations.add(new Consultation("CON069", "5", "DOC015", "19-09-2025", "Completed", "Arthritis management review"));
        consultations.add(new Consultation("CON070", "5", "DOC016", "20-09-2025", "Completed", "Respiratory function assessment"));
        consultations.add(new Consultation("CON071", "5", "DOC017", "21-09-2025", "Completed", "Kidney health evaluation"));
        consultations.add(new Consultation("CON072", "5", "DOC018", "22-09-2025", "Completed", "Blood disorder consultation"));
        consultations.add(new Consultation("CON073", "6", "DOC019", "23-09-2025", "Completed", "Infection treatment follow-up"));
        consultations.add(new Consultation("CON074", "6", "DOC020", "24-09-2025", "Completed", "Pain management consultation"));
        consultations.add(new Consultation("CON075", "6", "DOC001", "25-09-2025", "Completed", "Heart rhythm monitoring"));
        consultations.add(new Consultation("CON076", "6", "DOC002", "26-09-2025", "Completed", "Child development assessment"));
        consultations.add(new Consultation("CON077", "2", "DOC003", "27-09-2025", "Completed", "Memory and cognition test"));
        consultations.add(new Consultation("CON078", "7", "DOC005", "28-09-2025", "Completed", "Thyroid function evaluation"));
        consultations.add(new Consultation("CON079", "11", "DOC006", "29-09-2025", "Completed", "Anxiety disorder treatment"));
        consultations.add(new Consultation("CON080", "18", "DOC007", "30-09-2025", "Completed", "Skin cancer screening"));
        consultations.add(new Consultation("CON081", "1", "DOC008", "01-10-2025", "Completed", "Emergency chest pain evaluation"));
        consultations.add(new Consultation("CON082", "4", "DOC009", "02-10-2025", "Completed", "Tumor marker assessment"));
        consultations.add(new Consultation("CON083", "23", "DOC010", "03-10-2025", "Completed", "X-ray interpretation session"));
        consultations.add(new Consultation("CON084", "1", "DOC011", "04-10-2025", "Completed", "Reproductive health consultation"));
        consultations.add(new Consultation("CON085", "5", "DOC012", "05-10-2025", "Completed", "Bladder health assessment"));
        consultations.add(new Consultation("CON086", "24", "DOC013", "06-10-2025", "Completed", "Preventive medicine consultation"));
        consultations.add(new Consultation("CON087", "26", "DOC014", "07-10-2025", "Completed", "Liver function evaluation"));
        consultations.add(new Consultation("CON088", "5", "DOC015", "08-10-2025", "Completed", "Joint mobility assessment"));
        consultations.add(new Consultation("CON089", "10", "DOC016", "09-10-2025", "Completed", "Asthma control evaluation"));
        consultations.add(new Consultation("CON090", "10", "DOC017", "10-10-2025", "Completed", "Dialysis consultation"));
        consultations.add(new Consultation("CON091", "10", "DOC018", "11-10-2025", "Completed", "Anemia treatment review"));
        consultations.add(new Consultation("CON092", "10", "DOC019", "12-10-2025", "Completed", "Vaccination consultation"));
        consultations.add(new Consultation("CON093", "11", "DOC020", "13-10-2025", "Completed", "Surgical consultation"));
        consultations.add(new Consultation("CON094", "11", "DOC001", "14-10-2025", "Completed", "Hypertension medication adjustment"));
        consultations.add(new Consultation("CON095", "11", "DOC002", "15-10-2025", "Completed", "Growth hormone assessment"));
        consultations.add(new Consultation("CON096", "11", "DOC003", "16-10-2025", "Completed", "Seizure disorder evaluation"));
        consultations.add(new Consultation("CON097", "3", "DOC005", "17-10-2025", "Completed", "Insulin therapy consultation"));
        consultations.add(new Consultation("CON098", "8", "DOC006", "18-10-2025", "Completed", "Depression therapy session"));
        consultations.add(new Consultation("CON099", "15", "DOC007", "19-10-2025", "Completed", "Acne treatment consultation"));
        consultations.add(new Consultation("CON100", "20", "DOC008", "20-10-2025", "Completed", "Accident injury assessment"));
        consultations.add(new Consultation("CON101", "1", "DOC009", "21-10-2025", "Completed", "Chemotherapy consultation"));
        consultations.add(new Consultation("CON102", "3", "DOC010", "22-10-2025", "Completed", "MRI scan review"));
        consultations.add(new Consultation("CON103", "32", "DOC011", "23-10-2025", "Completed", "Menopause management"));
        consultations.add(new Consultation("CON104", "1", "DOC012", "24-10-2025", "Completed", "Kidney stone consultation"));
        consultations.add(new Consultation("CON105", "12", "DOC013", "25-10-2025", "Completed", "Cholesterol management"));
        consultations.add(new Consultation("CON106", "12", "DOC014", "26-10-2025", "Completed", "Inflammatory bowel disease"));
        consultations.add(new Consultation("CON107", "12", "DOC015", "27-10-2025", "Completed", "Osteoporosis screening"));
        consultations.add(new Consultation("CON108", "12", "DOC016", "28-10-2025", "Completed", "COPD management review"));
        consultations.add(new Consultation("CON109", "5", "DOC017", "29-10-2025", "Completed", "Chronic kidney disease"));
        consultations.add(new Consultation("CON110", "13", "DOC018", "30-10-2025", "Completed", "Leukemia follow-up"));
        consultations.add(new Consultation("CON111", "14", "DOC019", "31-10-2025", "Completed", "Hepatitis screening"));
        consultations.add(new Consultation("CON112", "5", "DOC020", "01-11-2025", "Completed", "Post-operative check"));
        consultations.add(new Consultation("CON113", "12", "DOC001", "02-11-2025", "Completed", "Arrhythmia monitoring"));
        consultations.add(new Consultation("CON114", "15", "DOC002", "03-11-2025", "Completed", "Developmental milestone check"));
        consultations.add(new Consultation("CON115", "16", "DOC003", "04-11-2025", "Completed", "Parkinson's disease assessment"));
        consultations.add(new Consultation("CON116", "12", "DOC005", "05-11-2025", "Completed", "Adrenal function test"));
        consultations.add(new Consultation("CON117", "2", "DOC006", "06-11-2025", "Completed", "Bipolar disorder management"));
        consultations.add(new Consultation("CON118", "7", "DOC007", "07-11-2025", "Completed", "Psoriasis treatment review"));
        consultations.add(new Consultation("CON119", "11", "DOC008", "08-11-2025", "Completed", "Cardiac arrest consultation"));
        consultations.add(new Consultation("CON120", "18", "DOC009", "09-11-2025", "Completed", "Radiation therapy planning"));
        consultations.add(new Consultation("CON121", "1", "DOC010", "10-11-2025", "Completed", "CT scan interpretation"));
        consultations.add(new Consultation("CON122", "1", "DOC011", "11-11-2025", "Completed", "Fertility consultation"));
        consultations.add(new Consultation("CON123", "1", "DOC012", "12-11-2025", "Completed", "Erectile dysfunction treatment"));
        consultations.add(new Consultation("CON124", "1", "DOC013", "13-11-2025", "Completed", "Metabolic syndrome evaluation"));
        consultations.add(new Consultation("CON125", "5", "DOC014", "14-11-2025", "Completed", "Peptic ulcer management"));
        consultations.add(new Consultation("CON126", "5", "DOC015", "15-11-2025", "Completed", "Fibromyalgia consultation"));
        consultations.add(new Consultation("CON127", "5", "DOC016", "16-11-2025", "Completed", "Sleep apnea evaluation"));
        consultations.add(new Consultation("CON128", "5", "DOC017", "17-11-2025", "Completed", "Hypertensive nephropathy"));
        consultations.add(new Consultation("CON129", "17", "DOC018", "18-11-2025", "Completed", "Thrombocytopenia treatment"));
        consultations.add(new Consultation("CON130", "18", "DOC019", "19-11-2025", "Completed", "Antibiotic resistance consultation"));
        consultations.add(new Consultation("CON131", "19", "DOC020", "20-11-2025", "Completed", "Chronic pain management"));
        consultations.add(new Consultation("CON132", "20", "DOC001", "21-11-2025", "Completed", "Valve replacement follow-up"));
        consultations.add(new Consultation("CON133", "21", "DOC002", "22-11-2025", "Completed", "Autism spectrum screening"));
        consultations.add(new Consultation("CON134", "22", "DOC003", "23-11-2025", "Completed", "Multiple sclerosis evaluation"));
        consultations.add(new Consultation("CON135", "23", "DOC005", "24-11-2025", "Completed", "Pituitary gland assessment"));
        consultations.add(new Consultation("CON136", "24", "DOC006", "25-11-2025", "Completed", "PTSD therapy session"));
        consultations.add(new Consultation("CON137", "11", "DOC007", "26-11-2025", "Completed", "Melanoma screening"));
        consultations.add(new Consultation("CON138", "8", "DOC008", "27-11-2025", "Completed", "Stroke rehabilitation"));
        consultations.add(new Consultation("CON139", "15", "DOC009", "28-11-2025", "Completed", "Immunotherapy consultation"));
        consultations.add(new Consultation("CON140", "20", "DOC010", "29-11-2025", "Completed", "Ultrasound examination"));
        consultations.add(new Consultation("CON141", "1", "DOC011", "30-11-2025", "Completed", "Endometriosis management"));
        consultations.add(new Consultation("CON142", "1", "DOC012", "01-12-2025", "Completed", "Benign prostatic hyperplasia"));
        consultations.add(new Consultation("CON143", "1", "DOC013", "02-12-2025", "Completed", "Vitamin deficiency assessment"));
        consultations.add(new Consultation("CON144", "1", "DOC014", "03-12-2025", "Completed", "Gallbladder disease consultation"));
        consultations.add(new Consultation("CON145", "25", "DOC015", "04-12-2025", "Completed", "Lupus management review"));
        consultations.add(new Consultation("CON146", "26", "DOC016", "05-12-2025", "Completed", "Pulmonary embolism treatment"));
        consultations.add(new Consultation("CON147", "27", "DOC017", "06-12-2025", "Completed", "Polycystic kidney disease"));
        consultations.add(new Consultation("CON148", "28", "DOC018", "07-12-2025", "Completed", "Hemophilia management"));
        consultations.add(new Consultation("CON149", "29", "DOC019", "08-12-2025", "Completed", "Tuberculosis screening"));
        consultations.add(new Consultation("CON150", "30", "DOC020", "09-12-2025", "Completed", "Regional anesthesia consultation"));
        consultations.add(new Consultation("CON151", "11", "DOC001", "10-12-2025", "Completed", "Congestive heart failure"));
        consultations.add(new Consultation("CON152", "32", "DOC002", "11-12-2025", "Completed", "ADHD assessment"));
        consultations.add(new Consultation("CON153", "33", "DOC003", "12-12-2025", "Completed", "Alzheimer's disease evaluation"));
        consultations.add(new Consultation("CON154", "34", "DOC005", "13-12-2025", "Completed", "Gestational diabetes management"));
        consultations.add(new Consultation("CON155", "35", "DOC006", "14-12-2025", "Completed", "Obsessive-compulsive disorder"));
        consultations.add(new Consultation("CON156", "36", "DOC007", "15-12-2025", "Completed", "Rosacea treatment consultation"));
        consultations.add(new Consultation("CON157", "2", "DOC008", "16-12-2025", "Completed", "Anaphylaxis management"));
        consultations.add(new Consultation("CON158", "7", "DOC009", "17-12-2025", "Completed", "Bone marrow biopsy consultation"));
        consultations.add(new Consultation("CON159", "11", "DOC010", "18-12-2025", "Completed", "Nuclear medicine scan"));
        consultations.add(new Consultation("CON160", "18", "DOC011", "19-12-2025", "Completed", "Ovarian cyst management"));
        consultations.add(new Consultation("CON161", "1", "DOC012", "20-12-2025", "Completed", "Testicular cancer screening"));
        consultations.add(new Consultation("CON162", "2", "DOC013", "21-12-2025", "Completed", "Autoimmune disease consultation"));
        consultations.add(new Consultation("CON163", "3", "DOC014", "22-12-2025", "Completed", "Celiac disease management"));
        consultations.add(new Consultation("CON164", "4", "DOC015", "23-12-2025", "Completed", "Gout management review"));
        consultations.add(new Consultation("CON165", "5", "DOC016", "24-12-2025", "Completed", "Interstitial lung disease"));
        consultations.add(new Consultation("CON166", "6", "DOC017", "25-12-2025", "Completed", "Diabetic nephropathy"));
        consultations.add(new Consultation("CON167", "7", "DOC018", "26-12-2025", "Completed", "Sickle cell disease"));
        consultations.add(new Consultation("CON168", "8", "DOC019", "27-12-2025", "Completed", "HIV consultation"));
        consultations.add(new Consultation("CON169", "9", "DOC020", "28-12-2025", "Completed", "Epidural injection consultation"));
        consultations.add(new Consultation("CON170", "10", "DOC001", "29-12-2025", "Completed", "Peripheral artery disease"));
        consultations.add(new Consultation("CON171", "11", "DOC002", "30-12-2025", "Completed", "Cystic fibrosis management"));
        consultations.add(new Consultation("CON172", "2", "DOC003", "31-12-2025", "Completed", "Huntington's disease consultation"));
        consultations.add(new Consultation("CON173", "13", "DOC005", "01-01-2026", "Completed", "Polycystic ovary syndrome"));
        consultations.add(new Consultation("CON174", "14", "DOC006", "02-01-2026", "Completed", "Eating disorder consultation"));
        consultations.add(new Consultation("CON175", "15", "DOC007", "03-01-2026", "Completed", "Vitiligo treatment review"));
        consultations.add(new Consultation("CON176", "16", "DOC008", "04-01-2026", "Completed", "Burn injury assessment"));
        consultations.add(new Consultation("CON177", "17", "DOC009", "05-01-2026", "Completed", "Palliative care consultation"));
        consultations.add(new Consultation("CON178", "18", "DOC010", "06-01-2026", "Completed", "Mammography interpretation"));
        consultations.add(new Consultation("CON179", "19", "DOC011", "07-01-2026", "Completed", "Uterine fibroid consultation"));
        consultations.add(new Consultation("CON180", "20", "DOC012", "08-01-2026", "Completed", "Vasectomy consultation"));
        consultations.add(new Consultation("CON181", "21", "DOC013", "09-01-2026", "Completed", "Iron deficiency anemia"));
        consultations.add(new Consultation("CON182", "22", "DOC014", "10-01-2026", "Completed", "Hepatitis C treatment"));
        consultations.add(new Consultation("CON183", "23", "DOC015", "11-01-2026", "Completed", "Carpal tunnel syndrome"));
        consultations.add(new Consultation("CON184", "24", "DOC016", "12-01-2026", "Completed", "Chronic bronchitis"));
        consultations.add(new Consultation("CON185", "25", "DOC017", "13-01-2026", "Completed", "Acute kidney injury"));
        consultations.add(new Consultation("CON186", "26", "DOC018", "14-01-2026", "Completed", "Platelet disorder consultation"));
        consultations.add(new Consultation("CON187", "27", "DOC019", "15-01-2026", "Completed", "Malaria treatment"));
        consultations.add(new Consultation("CON188", "28", "DOC020", "16-01-2026", "Completed", "Spinal anesthesia consultation"));
        consultations.add(new Consultation("CON189", "29", "DOC001", "17-01-2026", "Completed", "Mitral valve prolapse"));
        consultations.add(new Consultation("CON190", "30", "DOC002", "18-01-2026", "Completed", "Cerebral palsy management"));
        consultations.add(new Consultation("CON191", "31", "DOC003", "19-01-2026", "Completed", "Migraine prevention consultation"));
        consultations.add(new Consultation("CON192", "32", "DOC005", "20-01-2026", "Completed", "Cushing's syndrome evaluation"));
        consultations.add(new Consultation("CON193", "33", "DOC006", "21-01-2026", "Completed", "Panic disorder treatment"));
        consultations.add(new Consultation("CON194", "34", "DOC007", "22-01-2026", "Completed", "Basal cell carcinoma"));
        consultations.add(new Consultation("CON195", "35", "DOC008", "23-01-2026", "Completed", "Hypothermia treatment"));
        consultations.add(new Consultation("CON196", "36", "DOC009", "24-01-2026", "Completed", "Lymphoma consultation"));
        consultations.add(new Consultation("CON197", "37", "DOC010", "25-01-2026", "Completed", "Bone density scan"));
        consultations.add(new Consultation("CON198", "38", "DOC011", "26-01-2026", "Completed", "Pelvic inflammatory disease"));
        consultations.add(new Consultation("CON199", "39", "DOC012", "27-01-2026", "Completed", "Penile cancer screening"));
        consultations.add(new Consultation("CON200", "40", "DOC013", "28-01-2026", "Completed", "Chronic fatigue syndrome"));
        consultations.add(new Consultation("CON201", "1", "DOC014", "29-01-2026", "Completed", "Pancreatic disorder consultation"));
        consultations.add(new Consultation("CON202", "2", "DOC015", "30-01-2026", "Completed", "Tendonitis treatment"));
        consultations.add(new Consultation("CON203", "3", "DOC016", "31-01-2026", "Completed", "Pneumothorax management"));
        consultations.add(new Consultation("CON204", "4", "DOC017", "01-02-2026", "Completed", "Glomerulonephritis consultation"));
        consultations.add(new Consultation("CON205", "5", "DOC018", "02-02-2026", "Completed", "Aplastic anemia treatment"));
        consultations.add(new Consultation("CON206", "6", "DOC019", "03-02-2026", "Completed", "Dengue fever management"));
        consultations.add(new Consultation("CON207", "7", "DOC020", "04-02-2026", "Completed", "Nerve block consultation"));
        consultations.add(new Consultation("CON208", "8", "DOC001", "05-02-2026", "Completed", "Aortic stenosis evaluation"));
        consultations.add(new Consultation("CON209", "9", "DOC002", "06-02-2026", "Completed", "Down syndrome consultation"));
        consultations.add(new Consultation("CON210", "10", "DOC003", "07-02-2026", "Completed", "Trigeminal neuralgia"));
        consultations.add(new Consultation("CON211", "11", "DOC005", "08-02-2026", "Completed", "Addison's disease management"));
        consultations.add(new Consultation("CON212", "12", "DOC006", "09-02-2026", "Completed", "Schizophrenia treatment"));
        consultations.add(new Consultation("CON213", "13", "DOC007", "10-02-2026", "Completed", "Hidradenitis suppurativa"));
        consultations.add(new Consultation("CON214", "14", "DOC008", "11-02-2026", "Completed", "Sepsis management"));
        consultations.add(new Consultation("CON215", "15", "DOC009", "12-02-2026", "Completed", "Mesothelioma consultation"));
        consultations.add(new Consultation("CON216", "16", "DOC010", "13-02-2026", "Completed", "PET scan interpretation"));
        consultations.add(new Consultation("CON217", "17", "DOC011", "14-02-2026", "Completed", "Ectopic pregnancy management"));
        consultations.add(new Consultation("CON218", "18", "DOC012", "15-02-2026", "Completed", "Hydrocele treatment"));
        consultations.add(new Consultation("CON219", "19", "DOC013", "16-02-2026", "Completed", "Osteomalacia consultation"));
        consultations.add(new Consultation("CON220", "20", "DOC014", "17-02-2026", "Completed", "Esophageal cancer screening"));

        return consultations;
    }

    public static SetAndQueueInterface<Treatment> initializeSampleTreatments() {
        SetAndQueueInterface<Treatment> treatments = new SetQueueArray<>();

        treatments.add(new Treatment("TRE001", "1", "DOC001", "Hypertension", "10-07-2025"));
        treatments.add(new Treatment("TRE002", "3", "DOC003", "Migraine", "15-07-2025"));
        treatments.add(new Treatment("TRE003", "2", "DOC005", "Diabetes Type 2", "16-07-2025"));
        treatments.add(new Treatment("TRE004", "6", "DOC006", "Depression", "18-07-2025"));
        treatments.add(new Treatment("TRE005", "4", "DOC005", "Hypothyroidism", "19-07-2025"));
        treatments.add(new Treatment("TRE006", "8", "DOC002", "Childhood Asthma", "20-07-2025"));
        treatments.add(new Treatment("TRE007", "9", "DOC006", "Anxiety Disorder", "21-07-2025"));
        treatments.add(new Treatment("TRE008", "11", "DOC001", "High Cholesterol", "23-07-2025"));
        treatments.add(new Treatment("TRE009", "12", "DOC007", "Eczema", "24-07-2025"));
        treatments.add(new Treatment("TRE010", "13", "DOC008", "Acute Bronchitis", "25-07-2025"));
        treatments.add(new Treatment("TRE011", "14", "DOC009", "Cancer Screening", "26-07-2025"));
        treatments.add(new Treatment("TRE012", "15", "DOC010", "Fracture Assessment", "27-07-2025"));
        treatments.add(new Treatment("TRE013", "16", "DOC011", "Pregnancy Care", "28-07-2025"));
        treatments.add(new Treatment("TRE014", "17", "DOC012", "Urinary Tract Infection", "29-07-2025"));
        treatments.add(new Treatment("TRE015", "18", "DOC001", "Heart Disease Management", "30-07-2025"));
        treatments.add(new Treatment("TRE016", "19", "DOC013", "Gastritis", "02-08-2025"));
        treatments.add(new Treatment("TRE017", "20", "DOC014", "Inflammatory Bowel Disease", "03-08-2025"));
        treatments.add(new Treatment("TRE018", "21", "DOC015", "Rheumatoid Arthritis", "04-08-2025"));
        treatments.add(new Treatment("TRE019", "22", "DOC017", "Chronic Kidney Disease", "05-08-2025"));
        treatments.add(new Treatment("TRE020", "23", "DOC018", "Anemia", "06-08-2025"));
        treatments.add(new Treatment("TRE021", "24", "DOC019", "Pneumonia", "07-08-2025"));
        treatments.add(new Treatment("TRE022", "25", "DOC001", "Atrial Fibrillation", "08-08-2025"));
        treatments.add(new Treatment("TRE023", "26", "DOC007", "Psoriasis", "09-08-2025"));
        treatments.add(new Treatment("TRE024", "27", "DOC005", "Hyperthyroidism", "10-08-2025"));
        treatments.add(new Treatment("TRE025", "28", "DOC013", "Peptic Ulcer", "11-08-2025"));
        treatments.add(new Treatment("TRE026", "29", "DOC006", "Bipolar Disorder", "12-08-2025"));
        treatments.add(new Treatment("TRE027", "30", "DOC002", "Allergic Asthma", "13-08-2025"));
        treatments.add(new Treatment("TRE028", "31", "DOC008", "Acute Myocardial Infarction", "14-08-2025"));
        treatments.add(new Treatment("TRE029", "32", "DOC014", "Crohn's Disease", "15-08-2025"));
        treatments.add(new Treatment("TRE030", "33", "DOC015", "Osteoarthritis", "16-08-2025"));
        treatments.add(new Treatment("TRE031", "34", "DOC016", "Pulmonary Hypertension", "17-08-2025"));
        treatments.add(new Treatment("TRE032", "35", "DOC017", "Nephrotic Syndrome", "18-08-2025"));
        treatments.add(new Treatment("TRE033", "36", "DOC018", "Hemolytic Anemia", "19-08-2025"));
        treatments.add(new Treatment("TRE034", "37", "DOC019", "Meningitis", "20-08-2025"));
        treatments.add(new Treatment("TRE035", "38", "DOC020", "Chronic Pain Syndrome", "21-08-2025"));
        treatments.add(new Treatment("TRE036", "39", "DOC001", "Coronary Artery Disease", "22-08-2025"));
        treatments.add(new Treatment("TRE037", "40", "DOC002", "Attention Deficit Disorder", "23-08-2025"));
        treatments.add(new Treatment("TRE038", "1", "DOC003", "Epilepsy", "24-08-2025"));
        treatments.add(new Treatment("TRE039", "2", "DOC005", "Adrenal Insufficiency", "25-08-2025"));
        treatments.add(new Treatment("TRE040", "3", "DOC006", "Generalized Anxiety Disorder", "26-08-2025"));
        treatments.add(new Treatment("TRE041", "4", "DOC007", "Seborrheic Dermatitis", "27-08-2025"));
        treatments.add(new Treatment("TRE042", "5", "DOC008", "Acute Respiratory Distress", "28-08-2025"));
        treatments.add(new Treatment("TRE043", "6", "DOC009", "Breast Cancer", "29-08-2025"));
        treatments.add(new Treatment("TRE044", "7", "DOC010", "Bone Fracture", "30-08-2025"));
        treatments.add(new Treatment("TRE045", "8", "DOC011", "Endometriosis", "31-08-2025"));
        treatments.add(new Treatment("TRE046", "9", "DOC012", "Prostatitis", "01-09-2025"));
        treatments.add(new Treatment("TRE047", "10", "DOC013", "Hyperlipidemia", "02-09-2025"));
        treatments.add(new Treatment("TRE048", "11", "DOC014", "Gastroenteritis", "03-09-2025"));
        treatments.add(new Treatment("TRE049", "12", "DOC015", "Systemic Lupus Erythematosus", "04-09-2025"));
        treatments.add(new Treatment("TRE050", "13", "DOC016", "Chronic Obstructive Pulmonary Disease", "05-09-2025"));
        treatments.add(new Treatment("TRE051", "14", "DOC017", "Acute Renal Failure", "06-09-2025"));
        treatments.add(new Treatment("TRE052", "15", "DOC018", "Iron Deficiency Anemia", "07-09-2025"));
        treatments.add(new Treatment("TRE053", "16", "DOC019", "Viral Hepatitis", "08-09-2025"));
        treatments.add(new Treatment("TRE054", "17", "DOC020", "Postoperative Pain", "09-09-2025"));
        treatments.add(new Treatment("TRE055", "18", "DOC001", "Myocardial Infarction", "10-09-2025"));
        treatments.add(new Treatment("TRE056", "19", "DOC002", "Developmental Delay", "11-09-2025"));
        treatments.add(new Treatment("TRE057", "20", "DOC003", "Stroke Recovery", "12-09-2025"));
        treatments.add(new Treatment("TRE058", "21", "DOC005", "Type 1 Diabetes", "13-09-2025"));
        treatments.add(new Treatment("TRE059", "22", "DOC006", "Bipolar Disorder", "14-09-2025"));
        treatments.add(new Treatment("TRE060", "23", "DOC007", "Skin Cancer", "15-09-2025"));
        treatments.add(new Treatment("TRE061", "24", "DOC008", "Septic Shock", "16-09-2025"));
        treatments.add(new Treatment("TRE062", "25", "DOC009", "Lung Cancer", "17-09-2025"));
        treatments.add(new Treatment("TRE063", "26", "DOC010", "Osteoporosis", "18-09-2025"));
        treatments.add(new Treatment("TRE064", "27", "DOC011", "Ovarian Cancer", "19-09-2025"));
        treatments.add(new Treatment("TRE065", "28", "DOC012", "Bladder Cancer", "20-09-2025"));
        treatments.add(new Treatment("TRE066", "29", "DOC013", "Metabolic Syndrome", "21-09-2025"));
        treatments.add(new Treatment("TRE067", "30", "DOC014", "Liver Cirrhosis", "22-09-2025"));
        treatments.add(new Treatment("TRE068", "31", "DOC015", "Rheumatoid Arthritis", "23-09-2025"));
        treatments.add(new Treatment("TRE069", "32", "DOC016", "Pneumonia", "24-09-2025"));
        treatments.add(new Treatment("TRE070", "33", "DOC017", "Chronic Kidney Disease", "25-09-2025"));
        treatments.add(new Treatment("TRE071", "34", "DOC018", "Leukemia", "26-09-2025"));
        treatments.add(new Treatment("TRE072", "35", "DOC019", "Tuberculosis", "27-09-2025"));
        treatments.add(new Treatment("TRE073", "36", "DOC020", "Fibromyalgia", "28-09-2025"));
        treatments.add(new Treatment("TRE074", "37", "DOC001", "Atrial Fibrillation", "29-09-2025"));
        treatments.add(new Treatment("TRE075", "38", "DOC002", "Autism Spectrum Disorder", "30-09-2025"));
        treatments.add(new Treatment("TRE076", "39", "DOC003", "Parkinson's Disease", "01-10-2025"));
        treatments.add(new Treatment("TRE077", "40", "DOC005", "Thyroid Cancer", "02-10-2025"));
        treatments.add(new Treatment("TRE078", "1", "DOC006", "Post-Traumatic Stress Disorder", "03-10-2025"));
        treatments.add(new Treatment("TRE079", "2", "DOC007", "Melanoma", "04-10-2025"));
        treatments.add(new Treatment("TRE080", "3", "DOC008", "Cardiac Arrest", "05-10-2025"));
        treatments.add(new Treatment("TRE081", "4", "DOC009", "Colon Cancer", "06-10-2025"));
        treatments.add(new Treatment("TRE082", "5", "DOC010", "Spinal Injury", "07-10-2025"));
        treatments.add(new Treatment("TRE083", "6", "DOC011", "Cervical Cancer", "08-10-2025"));
        treatments.add(new Treatment("TRE084", "7", "DOC012", "Kidney Stones", "09-10-2025"));
        treatments.add(new Treatment("TRE085", "8", "DOC013", "Obesity", "10-10-2025"));
        treatments.add(new Treatment("TRE086", "9", "DOC014", "Inflammatory Bowel Disease", "11-10-2025"));
        treatments.add(new Treatment("TRE087", "10", "DOC015", "Ankylosing Spondylitis", "12-10-2025"));
        treatments.add(new Treatment("TRE088", "11", "DOC016", "Asthma", "13-10-2025"));
        treatments.add(new Treatment("TRE089", "12", "DOC017", "Polycystic Kidney Disease", "14-10-2025"));
        treatments.add(new Treatment("TRE090", "13", "DOC018", "Sickle Cell Disease", "15-10-2025"));
        treatments.add(new Treatment("TRE091", "14", "DOC019", "HIV/AIDS", "16-10-2025"));
        treatments.add(new Treatment("TRE092", "15", "DOC020", "Chronic Pain", "17-10-2025"));
        treatments.add(new Treatment("TRE093", "16", "DOC001", "Heart Failure", "18-10-2025"));
        treatments.add(new Treatment("TRE094", "17", "DOC002", "Growth Hormone Deficiency", "19-10-2025"));
        treatments.add(new Treatment("TRE095", "18", "DOC003", "Multiple Sclerosis", "20-10-2025"));
        treatments.add(new Treatment("TRE096", "19", "DOC005", "Gestational Diabetes", "21-10-2025"));
        treatments.add(new Treatment("TRE097", "20", "DOC006", "Major Depressive Disorder", "22-10-2025"));
        treatments.add(new Treatment("TRE098", "21", "DOC007", "Acne Vulgaris", "23-10-2025"));
        treatments.add(new Treatment("TRE099", "22", "DOC008", "Anaphylaxis", "24-10-2025"));
        treatments.add(new Treatment("TRE100", "23", "DOC009", "Pancreatic Cancer", "25-10-2025"));
        treatments.add(new Treatment("TRE101", "24", "DOC010", "Hip Fracture", "26-10-2025"));
        treatments.add(new Treatment("TRE102", "25", "DOC011", "Polycystic Ovary Syndrome", "27-10-2025"));
        treatments.add(new Treatment("TRE103", "26", "DOC012", "Erectile Dysfunction", "28-10-2025"));
        treatments.add(new Treatment("TRE104", "27", "DOC013", "Vitamin D Deficiency", "29-10-2025"));
        treatments.add(new Treatment("TRE105", "28", "DOC014", "Peptic Ulcer Disease", "30-10-2025"));
        treatments.add(new Treatment("TRE106", "29", "DOC015", "Osteoarthritis", "31-10-2025"));
        treatments.add(new Treatment("TRE107", "30", "DOC016", "Sleep Apnea", "01-11-2025"));
        treatments.add(new Treatment("TRE108", "31", "DOC017", "Diabetic Nephropathy", "02-11-2025"));
        treatments.add(new Treatment("TRE109", "32", "DOC018", "Thrombocytopenia", "03-11-2025"));
        treatments.add(new Treatment("TRE110", "33", "DOC019", "Malaria", "04-11-2025"));
        treatments.add(new Treatment("TRE111", "34", "DOC020", "Neuropathic Pain", "05-11-2025"));
        treatments.add(new Treatment("TRE112", "35", "DOC001", "Valvular Heart Disease", "06-11-2025"));
        treatments.add(new Treatment("TRE113", "36", "DOC002", "Cerebral Palsy", "07-11-2025"));
        treatments.add(new Treatment("TRE114", "37", "DOC003", "Alzheimer's Disease", "08-11-2025"));
        treatments.add(new Treatment("TRE115", "38", "DOC005", "Hyperthyroidism", "09-11-2025"));
        treatments.add(new Treatment("TRE116", "39", "DOC006", "Obsessive-Compulsive Disorder", "10-11-2025"));
        treatments.add(new Treatment("TRE117", "40", "DOC007", "Psoriasis", "11-11-2025"));
        treatments.add(new Treatment("TRE118", "1", "DOC008", "Burns", "12-11-2025"));
        treatments.add(new Treatment("TRE119", "2", "DOC009", "Brain Tumor", "13-11-2025"));
        treatments.add(new Treatment("TRE120", "3", "DOC010", "Compression Fracture", "14-11-2025"));
        treatments.add(new Treatment("TRE121", "4", "DOC011", "Menopause", "15-11-2025"));
        treatments.add(new Treatment("TRE122", "5", "DOC012", "Benign Prostatic Hyperplasia", "16-11-2025"));
        treatments.add(new Treatment("TRE123", "6", "DOC013", "Chronic Fatigue Syndrome", "17-11-2025"));
        treatments.add(new Treatment("TRE124", "7", "DOC014", "Gallstones", "18-11-2025"));
        treatments.add(new Treatment("TRE125", "8", "DOC015", "Gout", "19-11-2025"));
        treatments.add(new Treatment("TRE126", "9", "DOC016", "Interstitial Lung Disease", "20-11-2025"));
        treatments.add(new Treatment("TRE127", "10", "DOC017", "Hypertensive Nephropathy", "21-11-2025"));
        treatments.add(new Treatment("TRE128", "11", "DOC018", "Hemophilia", "22-11-2025"));
        treatments.add(new Treatment("TRE129", "12", "DOC019", "Dengue Fever", "23-11-2025"));
        treatments.add(new Treatment("TRE130", "13", "DOC020", "Spinal Stenosis", "24-11-2025"));
        treatments.add(new Treatment("TRE131", "14", "DOC001", "Peripheral Artery Disease", "25-11-2025"));
        treatments.add(new Treatment("TRE132", "15", "DOC002", "Cystic Fibrosis", "26-11-2025"));
        treatments.add(new Treatment("TRE133", "16", "DOC003", "Huntington's Disease", "27-11-2025"));
        treatments.add(new Treatment("TRE134", "17", "DOC005", "Cushing's Syndrome", "28-11-2025"));
        treatments.add(new Treatment("TRE135", "18", "DOC006", "Panic Disorder", "29-11-2025"));
        treatments.add(new Treatment("TRE136", "19", "DOC007", "Basal Cell Carcinoma", "30-11-2025"));
        treatments.add(new Treatment("TRE137", "20", "DOC008", "Hypothermia", "01-12-2025"));
        treatments.add(new Treatment("TRE138", "21", "DOC009", "Lymphoma", "02-12-2025"));
        treatments.add(new Treatment("TRE139", "22", "DOC010", "Bone Metastases", "03-12-2025"));
        treatments.add(new Treatment("TRE140", "23", "DOC011", "Pelvic Inflammatory Disease", "04-12-2025"));
        treatments.add(new Treatment("TRE141", "24", "DOC012", "Testicular Cancer", "05-12-2025"));
        treatments.add(new Treatment("TRE142", "25", "DOC013", "Autoimmune Hepatitis", "06-12-2025"));
        treatments.add(new Treatment("TRE143", "26", "DOC014", "Celiac Disease", "07-12-2025"));
        treatments.add(new Treatment("TRE144", "27", "DOC015", "Carpal Tunnel Syndrome", "08-12-2025"));
        treatments.add(new Treatment("TRE145", "28", "DOC016", "Pulmonary Embolism", "09-12-2025"));
        treatments.add(new Treatment("TRE146", "29", "DOC017", "Acute Kidney Injury", "10-12-2025"));
        treatments.add(new Treatment("TRE147", "30", "DOC018", "Aplastic Anemia", "11-12-2025"));
        treatments.add(new Treatment("TRE148", "31", "DOC019", "Chikungunya", "12-12-2025"));
        treatments.add(new Treatment("TRE149", "32", "DOC020", "Complex Regional Pain Syndrome", "13-12-2025"));
        treatments.add(new Treatment("TRE150", "33", "DOC001", "Aortic Stenosis", "14-12-2025"));
        treatments.add(new Treatment("TRE151", "34", "DOC002", "Down Syndrome", "15-12-2025"));
        treatments.add(new Treatment("TRE152", "35", "DOC003", "Trigeminal Neuralgia", "16-12-2025"));
        treatments.add(new Treatment("TRE153", "36", "DOC005", "Addison's Disease", "17-12-2025"));
        treatments.add(new Treatment("TRE154", "37", "DOC006", "Schizophrenia", "18-12-2025"));
        treatments.add(new Treatment("TRE155", "38", "DOC007", "Hidradenitis Suppurativa", "19-12-2025"));
        treatments.add(new Treatment("TRE156", "39", "DOC008", "Sepsis", "20-12-2025"));
        treatments.add(new Treatment("TRE157", "40", "DOC009", "Mesothelioma", "21-12-2025"));
        treatments.add(new Treatment("TRE158", "1", "DOC010", "Bone Density Loss", "22-12-2025"));
        treatments.add(new Treatment("TRE159", "2", "DOC011", "Uterine Fibroids", "23-12-2025"));
        treatments.add(new Treatment("TRE160", "3", "DOC012", "Hydrocele", "24-12-2025"));
        treatments.add(new Treatment("TRE161", "4", "DOC013", "Osteomalacia", "25-12-2025"));
        treatments.add(new Treatment("TRE162", "5", "DOC014", "Esophageal Cancer", "26-12-2025"));
        treatments.add(new Treatment("TRE163", "6", "DOC015", "Tendonitis", "27-12-2025"));
        treatments.add(new Treatment("TRE164", "7", "DOC016", "Pneumothorax", "28-12-2025"));
        treatments.add(new Treatment("TRE165", "8", "DOC017", "Glomerulonephritis", "29-12-2025"));
        treatments.add(new Treatment("TRE166", "9", "DOC018", "Platelet Dysfunction", "30-12-2025"));
        treatments.add(new Treatment("TRE167", "10", "DOC019", "Zika Virus", "31-12-2025"));
        treatments.add(new Treatment("TRE168", "11", "DOC020", "Chronic Back Pain", "01-01-2026"));
        treatments.add(new Treatment("TRE169", "12", "DOC001", "Mitral Valve Prolapse", "02-01-2026"));
        treatments.add(new Treatment("TRE170", "13", "DOC002", "Attention Deficit Hyperactivity Disorder", "03-01-2026"));
        treatments.add(new Treatment("TRE171", "14", "DOC003", "Migraine Prophylaxis", "04-01-2026"));
        treatments.add(new Treatment("TRE172", "15", "DOC005", "Polycystic Ovary Syndrome", "05-01-2026"));
        treatments.add(new Treatment("TRE173", "16", "DOC006", "Eating Disorders", "06-01-2026"));
        treatments.add(new Treatment("TRE174", "17", "DOC007", "Vitiligo", "07-01-2026"));
        treatments.add(new Treatment("TRE175", "18", "DOC008", "Burn Injuries", "08-01-2026"));
        treatments.add(new Treatment("TRE176", "19", "DOC009", "Palliative Care", "09-01-2026"));
        treatments.add(new Treatment("TRE177", "20", "DOC010", "Soft Tissue Injury", "10-01-2026"));
        treatments.add(new Treatment("TRE178", "21", "DOC011", "Ovarian Cysts", "11-01-2026"));
        treatments.add(new Treatment("TRE179", "22", "DOC012", "Vasectomy Recovery", "12-01-2026"));
        treatments.add(new Treatment("TRE180", "23", "DOC013", "Iron Deficiency", "13-01-2026"));
        treatments.add(new Treatment("TRE181", "24", "DOC014", "Hepatitis C", "14-01-2026"));
        treatments.add(new Treatment("TRE182", "25", "DOC015", "Joint Replacement Recovery", "15-01-2026"));
        treatments.add(new Treatment("TRE183", "26", "DOC016", "Chronic Bronchitis", "16-01-2026"));
        treatments.add(new Treatment("TRE184", "27", "DOC017", "Renal Calculi", "17-01-2026"));
        treatments.add(new Treatment("TRE185", "28", "DOC018", "Blood Coagulation Disorder", "18-01-2026"));
        treatments.add(new Treatment("TRE186", "29", "DOC019", "Viral Infection", "19-01-2026"));
        treatments.add(new Treatment("TRE187", "30", "DOC020", "Post-Surgical Pain", "20-01-2026"));
        treatments.add(new Treatment("TRE188", "31", "DOC001", "Coronary Stent Follow-up", "21-01-2026"));
        treatments.add(new Treatment("TRE189", "32", "DOC002", "Developmental Disorders", "22-01-2026"));
        treatments.add(new Treatment("TRE190", "33", "DOC003", "Cognitive Impairment", "23-01-2026"));
        treatments.add(new Treatment("TRE191", "34", "DOC005", "Hormonal Imbalance", "24-01-2026"));
        treatments.add(new Treatment("TRE192", "35", "DOC006", "Substance Abuse Recovery", "25-01-2026"));
        treatments.add(new Treatment("TRE193", "36", "DOC007", "Skin Allergies", "26-01-2026"));
        treatments.add(new Treatment("TRE194", "37", "DOC008", "Electrolyte Imbalance", "27-01-2026"));
        treatments.add(new Treatment("TRE195", "38", "DOC009", "Radiation Side Effects", "28-01-2026"));
        treatments.add(new Treatment("TRE196", "39", "DOC010", "Muscle Strain", "29-01-2026"));
        treatments.add(new Treatment("TRE197", "40", "DOC011", "Reproductive Health Issues", "30-01-2026"));
        treatments.add(new Treatment("TRE198", "1", "DOC012", "Urinary Incontinence", "31-01-2026"));
        treatments.add(new Treatment("TRE199", "2", "DOC013", "Nutritional Deficiency", "01-02-2026"));
        treatments.add(new Treatment("TRE200", "3", "DOC014", "Digestive Disorders", "02-02-2026"));
        treatments.add(new Treatment("TRE201", "4", "DOC015", "Muscle Weakness", "03-02-2026"));
        treatments.add(new Treatment("TRE202", "5", "DOC016", "Respiratory Infection", "04-02-2026"));
        treatments.add(new Treatment("TRE203", "6", "DOC017", "Fluid Retention", "05-02-2026"));
        treatments.add(new Treatment("TRE204", "7", "DOC018", "Blood Pressure Irregularities", "06-02-2026"));
        treatments.add(new Treatment("TRE205", "8", "DOC019", "Infectious Disease Prevention", "07-02-2026"));
        treatments.add(new Treatment("TRE206", "9", "DOC020", "Nerve Compression", "08-02-2026"));
        treatments.add(new Treatment("TRE207", "10", "DOC001", "Heart Rhythm Disorders", "09-02-2026"));
        treatments.add(new Treatment("TRE208", "11", "DOC002", "Learning Disabilities", "10-02-2026"));
        treatments.add(new Treatment("TRE209", "12", "DOC003", "Movement Disorders", "11-02-2026"));
        treatments.add(new Treatment("TRE210", "13", "DOC005", "Metabolic Disorders", "12-02-2026"));

        return treatments;
    }

    public static SetAndQueueInterface<PharmacyTransaction> initializeSampleTransactions() {
        SetAndQueueInterface<PharmacyTransaction> transactions = new SetQueueArray<>();

        transactions.add(new PharmacyTransaction("TXN001", "1", "MED007", 2, "10-07-2025"));
        transactions.add(new PharmacyTransaction("TXN002", "3", "MED003", 1, "15-07-2025"));
        transactions.add(new PharmacyTransaction("TXN003", "2", "MED006", 3, "16-07-2025"));
        transactions.add(new PharmacyTransaction("TXN004", "6", "MED009", 1, "18-07-2025"));
        transactions.add(new PharmacyTransaction("TXN005", "4", "MED004", 2, "19-07-2025"));
        transactions.add(new PharmacyTransaction("TXN006", "8", "MED008", 1, "20-07-2025"));
        transactions.add(new PharmacyTransaction("TXN007", "9", "MED009", 2, "21-07-2025"));
        transactions.add(new PharmacyTransaction("TXN008", "11", "MED010", 1, "23-07-2025"));
        transactions.add(new PharmacyTransaction("TXN009", "12", "MED003", 1, "24-07-2025"));
        transactions.add(new PharmacyTransaction("TXN010", "5", "MED007", 1, "25-07-2025"));
        transactions.add(new PharmacyTransaction("TXN011", "7", "MED001", 2, "26-07-2025"));
        transactions.add(new PharmacyTransaction("TXN012", "10", "MED005", 1, "27-07-2025"));
        transactions.add(new PharmacyTransaction("TXN013", "13", "MED002", 1, "28-07-2025"));
        transactions.add(new PharmacyTransaction("TXN014", "14", "MED001", 1, "29-07-2025"));
        transactions.add(new PharmacyTransaction("TXN015", "15", "MED003", 2, "30-07-2025"));
        transactions.add(new PharmacyTransaction("TXN016", "16", "MED013", 1, "31-07-2025"));
        transactions.add(new PharmacyTransaction("TXN017", "17", "MED014", 2, "01-08-2025"));
        transactions.add(new PharmacyTransaction("TXN018", "18", "MED015", 1, "02-08-2025"));
        transactions.add(new PharmacyTransaction("TXN019", "19", "MED016", 1, "03-08-2025"));
        transactions.add(new PharmacyTransaction("TXN020", "20", "MED017", 1, "04-08-2025"));
        transactions.add(new PharmacyTransaction("TXN021", "21", "MED018", 2, "05-08-2025"));
        transactions.add(new PharmacyTransaction("TXN022", "22", "MED019", 1, "06-08-2025"));
        transactions.add(new PharmacyTransaction("TXN023", "23", "MED020", 1, "07-08-2025"));
        transactions.add(new PharmacyTransaction("TXN024", "24", "MED011", 2, "08-08-2025"));
        transactions.add(new PharmacyTransaction("TXN025", "25", "MED012", 1, "09-08-2025"));
        transactions.add(new PharmacyTransaction("TXN026", "26", "MED022", 1, "10-08-2025"));
        transactions.add(new PharmacyTransaction("TXN027", "27", "MED023", 2, "11-08-2025"));
        transactions.add(new PharmacyTransaction("TXN028", "28", "MED024", 1, "12-08-2025"));
        transactions.add(new PharmacyTransaction("TXN029", "29", "MED025", 1, "13-08-2025"));
        transactions.add(new PharmacyTransaction("TXN030", "30", "MED026", 2, "14-08-2025"));
        transactions.add(new PharmacyTransaction("TXN031", "31", "MED027", 1, "15-08-2025"));
        transactions.add(new PharmacyTransaction("TXN032", "32", "MED028", 1, "16-08-2025"));
        transactions.add(new PharmacyTransaction("TXN033", "33", "MED029", 2, "17-08-2025"));
        transactions.add(new PharmacyTransaction("TXN034", "34", "MED030", 1, "18-08-2025"));
        transactions.add(new PharmacyTransaction("TXN035", "35", "MED031", 1, "19-08-2025"));
        transactions.add(new PharmacyTransaction("TXN036", "36", "MED032", 2, "20-08-2025"));
        transactions.add(new PharmacyTransaction("TXN037", "37", "MED033", 1, "21-08-2025"));
        transactions.add(new PharmacyTransaction("TXN038", "38", "MED034", 1, "22-08-2025"));
        transactions.add(new PharmacyTransaction("TXN039", "39", "MED035", 2, "23-08-2025"));
        transactions.add(new PharmacyTransaction("TXN040", "40", "MED036", 1, "24-08-2025"));
        transactions.add(new PharmacyTransaction("TXN041", "1", "MED001", 3, "25-08-2025"));
        transactions.add(new PharmacyTransaction("TXN042", "2", "MED002", 2, "26-08-2025"));
        transactions.add(new PharmacyTransaction("TXN043", "3", "MED003", 1, "27-08-2025"));
        transactions.add(new PharmacyTransaction("TXN044", "4", "MED004", 2, "28-08-2025"));
        transactions.add(new PharmacyTransaction("TXN045", "5", "MED005", 1, "29-08-2025"));
        transactions.add(new PharmacyTransaction("TXN046", "6", "MED006", 3, "30-08-2025"));
        transactions.add(new PharmacyTransaction("TXN047", "7", "MED007", 1, "31-08-2025"));
        transactions.add(new PharmacyTransaction("TXN048", "8", "MED008", 2, "01-09-2025"));
        transactions.add(new PharmacyTransaction("TXN049", "9", "MED009", 1, "02-09-2025"));
        transactions.add(new PharmacyTransaction("TXN050", "10", "MED010", 2, "03-09-2025"));
        transactions.add(new PharmacyTransaction("TXN051", "11", "MED011", 3, "04-09-2025"));
        transactions.add(new PharmacyTransaction("TXN052", "12", "MED012", 1, "05-09-2025"));
        transactions.add(new PharmacyTransaction("TXN053", "13", "MED013", 2, "06-09-2025"));
        transactions.add(new PharmacyTransaction("TXN054", "14", "MED014", 1, "07-09-2025"));
        transactions.add(new PharmacyTransaction("TXN055", "15", "MED015", 2, "08-09-2025"));
        transactions.add(new PharmacyTransaction("TXN056", "16", "MED016", 1, "09-09-2025"));
        transactions.add(new PharmacyTransaction("TXN057", "17", "MED017", 3, "10-09-2025"));
        transactions.add(new PharmacyTransaction("TXN058", "18", "MED018", 2, "11-09-2025"));
        transactions.add(new PharmacyTransaction("TXN059", "19", "MED019", 1, "12-09-2025"));
        transactions.add(new PharmacyTransaction("TXN060", "20", "MED020", 2, "13-09-2025"));
        transactions.add(new PharmacyTransaction("TXN061", "21", "MED021", 1, "14-09-2025"));
        transactions.add(new PharmacyTransaction("TXN062", "22", "MED022", 3, "15-09-2025"));
        transactions.add(new PharmacyTransaction("TXN063", "23", "MED023", 2, "16-09-2025"));
        transactions.add(new PharmacyTransaction("TXN064", "24", "MED024", 1, "17-09-2025"));
        transactions.add(new PharmacyTransaction("TXN065", "25", "MED025", 2, "18-09-2025"));
        transactions.add(new PharmacyTransaction("TXN066", "26", "MED026", 1, "19-09-2025"));
        transactions.add(new PharmacyTransaction("TXN067", "27", "MED027", 3, "20-09-2025"));
        transactions.add(new PharmacyTransaction("TXN068", "28", "MED028", 1, "21-09-2025"));
        transactions.add(new PharmacyTransaction("TXN069", "29", "MED029", 2, "22-09-2025"));
        transactions.add(new PharmacyTransaction("TXN070", "30", "MED030", 1, "23-09-2025"));
        transactions.add(new PharmacyTransaction("TXN071", "31", "MED031", 2, "24-09-2025"));
        transactions.add(new PharmacyTransaction("TXN072", "32", "MED032", 3, "25-09-2025"));
        transactions.add(new PharmacyTransaction("TXN073", "33", "MED033", 1, "26-09-2025"));
        transactions.add(new PharmacyTransaction("TXN074", "34", "MED034", 2, "27-09-2025"));
        transactions.add(new PharmacyTransaction("TXN075", "35", "MED035", 1, "28-09-2025"));
        transactions.add(new PharmacyTransaction("TXN076", "36", "MED036", 3, "29-09-2025"));
        transactions.add(new PharmacyTransaction("TXN077", "37", "MED037", 2, "30-09-2025"));
        transactions.add(new PharmacyTransaction("TXN078", "38", "MED038", 1, "01-10-2025"));
        transactions.add(new PharmacyTransaction("TXN079", "39", "MED039", 2, "02-10-2025"));
        transactions.add(new PharmacyTransaction("TXN080", "40", "MED040", 1, "03-10-2025"));
        transactions.add(new PharmacyTransaction("TXN081", "1", "MED001", 2, "04-10-2025"));
        transactions.add(new PharmacyTransaction("TXN082", "2", "MED002", 3, "05-10-2025"));
        transactions.add(new PharmacyTransaction("TXN083", "3", "MED003", 1, "06-10-2025"));
        transactions.add(new PharmacyTransaction("TXN084", "4", "MED004", 2, "07-10-2025"));
        transactions.add(new PharmacyTransaction("TXN085", "5", "MED005", 1, "08-10-2025"));
        transactions.add(new PharmacyTransaction("TXN086", "6", "MED006", 3, "09-10-2025"));
        transactions.add(new PharmacyTransaction("TXN087", "7", "MED007", 2, "10-10-2025"));
        transactions.add(new PharmacyTransaction("TXN088", "8", "MED008", 1, "11-10-2025"));
        transactions.add(new PharmacyTransaction("TXN089", "9", "MED009", 2, "12-10-2025"));
        transactions.add(new PharmacyTransaction("TXN090", "10", "MED010", 1, "13-10-2025"));
        transactions.add(new PharmacyTransaction("TXN091", "11", "MED011", 3, "14-10-2025"));
        transactions.add(new PharmacyTransaction("TXN092", "12", "MED012", 2, "15-10-2025"));
        transactions.add(new PharmacyTransaction("TXN093", "13", "MED013", 1, "16-10-2025"));
        transactions.add(new PharmacyTransaction("TXN094", "14", "MED014", 2, "17-10-2025"));
        transactions.add(new PharmacyTransaction("TXN095", "15", "MED015", 3, "18-10-2025"));
        transactions.add(new PharmacyTransaction("TXN096", "16", "MED016", 1, "19-10-2025"));
        transactions.add(new PharmacyTransaction("TXN097", "17", "MED017", 2, "20-10-2025"));
        transactions.add(new PharmacyTransaction("TXN098", "18", "MED018", 1, "21-10-2025"));
        transactions.add(new PharmacyTransaction("TXN099", "19", "MED019", 3, "22-10-2025"));
        transactions.add(new PharmacyTransaction("TXN100", "20", "MED020", 2, "23-10-2025"));
        transactions.add(new PharmacyTransaction("TXN101", "21", "MED021", 1, "24-10-2025"));
        transactions.add(new PharmacyTransaction("TXN102", "22", "MED022", 2, "25-10-2025"));
        transactions.add(new PharmacyTransaction("TXN103", "23", "MED023", 3, "26-10-2025"));
        transactions.add(new PharmacyTransaction("TXN104", "24", "MED024", 1, "27-10-2025"));
        transactions.add(new PharmacyTransaction("TXN105", "25", "MED025", 2, "28-10-2025"));
        transactions.add(new PharmacyTransaction("TXN106", "26", "MED026", 1, "29-10-2025"));
        transactions.add(new PharmacyTransaction("TXN107", "27", "MED027", 3, "30-10-2025"));
        transactions.add(new PharmacyTransaction("TXN108", "28", "MED028", 2, "31-10-2025"));
        transactions.add(new PharmacyTransaction("TXN109", "29", "MED029", 1, "01-11-2025"));
        transactions.add(new PharmacyTransaction("TXN110", "30", "MED030", 2, "02-11-2025"));
        transactions.add(new PharmacyTransaction("TXN111", "31", "MED031", 3, "03-11-2025"));
        transactions.add(new PharmacyTransaction("TXN112", "32", "MED032", 1, "04-11-2025"));
        transactions.add(new PharmacyTransaction("TXN113", "33", "MED033", 2, "05-11-2025"));
        transactions.add(new PharmacyTransaction("TXN114", "34", "MED034", 1, "06-11-2025"));
        transactions.add(new PharmacyTransaction("TXN115", "35", "MED035", 3, "07-11-2025"));
        transactions.add(new PharmacyTransaction("TXN116", "36", "MED036", 2, "08-11-2025"));
        transactions.add(new PharmacyTransaction("TXN117", "37", "MED037", 1, "09-11-2025"));
        transactions.add(new PharmacyTransaction("TXN118", "38", "MED038", 2, "10-11-2025"));
        transactions.add(new PharmacyTransaction("TXN119", "39", "MED039", 3, "11-11-2025"));
        transactions.add(new PharmacyTransaction("TXN120", "40", "MED040", 1, "12-11-2025"));
        transactions.add(new PharmacyTransaction("TXN121", "1", "MED001", 2, "13-11-2025"));
        transactions.add(new PharmacyTransaction("TXN122", "2", "MED002", 1, "14-11-2025"));
        transactions.add(new PharmacyTransaction("TXN123", "3", "MED003", 3, "15-11-2025"));
        transactions.add(new PharmacyTransaction("TXN124", "4", "MED004", 2, "16-11-2025"));
        transactions.add(new PharmacyTransaction("TXN125", "5", "MED005", 1, "17-11-2025"));
        transactions.add(new PharmacyTransaction("TXN126", "6", "MED006", 2, "18-11-2025"));
        transactions.add(new PharmacyTransaction("TXN127", "7", "MED007", 3, "19-11-2025"));
        transactions.add(new PharmacyTransaction("TXN128", "8", "MED008", 1, "20-11-2025"));
        transactions.add(new PharmacyTransaction("TXN129", "9", "MED009", 2, "21-11-2025"));
        transactions.add(new PharmacyTransaction("TXN130", "10", "MED010", 1, "22-11-2025"));
        transactions.add(new PharmacyTransaction("TXN131", "11", "MED011", 3, "23-11-2025"));
        transactions.add(new PharmacyTransaction("TXN132", "12", "MED012", 2, "24-11-2025"));
        transactions.add(new PharmacyTransaction("TXN133", "13", "MED013", 1, "25-11-2025"));
        transactions.add(new PharmacyTransaction("TXN134", "14", "MED014", 2, "26-11-2025"));
        transactions.add(new PharmacyTransaction("TXN135", "15", "MED015", 3, "27-11-2025"));
        transactions.add(new PharmacyTransaction("TXN136", "16", "MED016", 1, "28-11-2025"));
        transactions.add(new PharmacyTransaction("TXN137", "17", "MED017", 2, "29-11-2025"));
        transactions.add(new PharmacyTransaction("TXN138", "18", "MED018", 1, "30-11-2025"));
        transactions.add(new PharmacyTransaction("TXN139", "19", "MED019", 3, "01-12-2025"));
        transactions.add(new PharmacyTransaction("TXN140", "20", "MED020", 2, "02-12-2025"));
        transactions.add(new PharmacyTransaction("TXN141", "21", "MED021", 1, "03-12-2025"));
        transactions.add(new PharmacyTransaction("TXN142", "22", "MED022", 2, "04-12-2025"));
        transactions.add(new PharmacyTransaction("TXN143", "23", "MED023", 3, "05-12-2025"));
        transactions.add(new PharmacyTransaction("TXN144", "24", "MED024", 1, "06-12-2025"));
        transactions.add(new PharmacyTransaction("TXN145", "25", "MED025", 2, "07-12-2025"));
        transactions.add(new PharmacyTransaction("TXN146", "26", "MED026", 1, "08-12-2025"));
        transactions.add(new PharmacyTransaction("TXN147", "27", "MED027", 3, "09-12-2025"));
        transactions.add(new PharmacyTransaction("TXN148", "28", "MED028", 2, "10-12-2025"));
        transactions.add(new PharmacyTransaction("TXN149", "29", "MED029", 1, "11-12-2025"));
        transactions.add(new PharmacyTransaction("TXN150", "30", "MED030", 2, "12-12-2025"));
        transactions.add(new PharmacyTransaction("TXN151", "31", "MED031", 3, "13-12-2025"));
        transactions.add(new PharmacyTransaction("TXN152", "32", "MED032", 1, "14-12-2025"));
        transactions.add(new PharmacyTransaction("TXN153", "33", "MED033", 2, "15-12-2025"));
        transactions.add(new PharmacyTransaction("TXN154", "34", "MED034", 1, "16-12-2025"));
        transactions.add(new PharmacyTransaction("TXN155", "35", "MED035", 3, "17-12-2025"));
        transactions.add(new PharmacyTransaction("TXN156", "36", "MED036", 2, "18-12-2025"));
        transactions.add(new PharmacyTransaction("TXN157", "37", "MED037", 1, "19-12-2025"));
        transactions.add(new PharmacyTransaction("TXN158", "38", "MED038", 2, "20-12-2025"));
        transactions.add(new PharmacyTransaction("TXN159", "39", "MED039", 3, "21-12-2025"));
        transactions.add(new PharmacyTransaction("TXN160", "40", "MED040", 1, "22-12-2025"));
        transactions.add(new PharmacyTransaction("TXN161", "1", "MED001", 2, "23-12-2025"));
        transactions.add(new PharmacyTransaction("TXN162", "2", "MED002", 1, "24-12-2025"));
        transactions.add(new PharmacyTransaction("TXN163", "3", "MED003", 3, "25-12-2025"));
        transactions.add(new PharmacyTransaction("TXN164", "4", "MED004", 2, "26-12-2025"));
        transactions.add(new PharmacyTransaction("TXN165", "5", "MED005", 1, "27-12-2025"));
        transactions.add(new PharmacyTransaction("TXN166", "6", "MED006", 2, "28-12-2025"));
        transactions.add(new PharmacyTransaction("TXN167", "7", "MED007", 3, "29-12-2025"));
        transactions.add(new PharmacyTransaction("TXN168", "8", "MED008", 1, "30-12-2025"));
        transactions.add(new PharmacyTransaction("TXN169", "9", "MED009", 2, "31-12-2025"));
        transactions.add(new PharmacyTransaction("TXN170", "10", "MED010", 1, "01-01-2026"));
        transactions.add(new PharmacyTransaction("TXN171", "11", "MED011", 3, "02-01-2026"));
        transactions.add(new PharmacyTransaction("TXN172", "12", "MED012", 2, "03-01-2026"));
        transactions.add(new PharmacyTransaction("TXN173", "13", "MED013", 1, "04-01-2026"));
        transactions.add(new PharmacyTransaction("TXN174", "14", "MED014", 2, "05-01-2026"));
        transactions.add(new PharmacyTransaction("TXN175", "15", "MED015", 3, "06-01-2026"));
        transactions.add(new PharmacyTransaction("TXN176", "16", "MED016", 1, "07-01-2026"));
        transactions.add(new PharmacyTransaction("TXN177", "17", "MED017", 2, "08-01-2026"));
        transactions.add(new PharmacyTransaction("TXN178", "18", "MED018", 1, "09-01-2026"));
        transactions.add(new PharmacyTransaction("TXN179", "19", "MED019", 3, "10-01-2026"));
        transactions.add(new PharmacyTransaction("TXN180", "20", "MED020", 2, "11-01-2026"));
        transactions.add(new PharmacyTransaction("TXN181", "21", "MED021", 1, "12-01-2026"));
        transactions.add(new PharmacyTransaction("TXN182", "22", "MED022", 2, "13-01-2026"));
        transactions.add(new PharmacyTransaction("TXN183", "23", "MED023", 3, "14-01-2026"));
        transactions.add(new PharmacyTransaction("TXN184", "24", "MED024", 1, "15-01-2026"));
        transactions.add(new PharmacyTransaction("TXN185", "25", "MED025", 2, "16-01-2026"));
        transactions.add(new PharmacyTransaction("TXN186", "26", "MED026", 1, "17-01-2026"));
        transactions.add(new PharmacyTransaction("TXN187", "27", "MED027", 3, "18-01-2026"));
        transactions.add(new PharmacyTransaction("TXN188", "28", "MED028", 2, "19-01-2026"));
        transactions.add(new PharmacyTransaction("TXN189", "29", "MED029", 1, "20-01-2026"));
        transactions.add(new PharmacyTransaction("TXN190", "30", "MED030", 2, "21-01-2026"));
        transactions.add(new PharmacyTransaction("TXN191", "31", "MED031", 3, "22-01-2026"));
        transactions.add(new PharmacyTransaction("TXN192", "32", "MED032", 1, "23-01-2026"));
        transactions.add(new PharmacyTransaction("TXN193", "33", "MED033", 2, "24-01-2026"));
        transactions.add(new PharmacyTransaction("TXN194", "34", "MED034", 1, "25-01-2026"));
        transactions.add(new PharmacyTransaction("TXN195", "35", "MED035", 3, "26-01-2026"));
        transactions.add(new PharmacyTransaction("TXN196", "36", "MED036", 2, "27-01-2026"));
        transactions.add(new PharmacyTransaction("TXN197", "37", "MED037", 1, "28-01-2026"));
        transactions.add(new PharmacyTransaction("TXN198", "38", "MED038", 2, "29-01-2026"));
        transactions.add(new PharmacyTransaction("TXN199", "39", "MED039", 3, "30-01-2026"));
        transactions.add(new PharmacyTransaction("TXN200", "40", "MED040", 1, "31-01-2026"));
        transactions.add(new PharmacyTransaction("TXN201", "1", "MED001", 2, "01-02-2026"));
        transactions.add(new PharmacyTransaction("TXN202", "2", "MED002", 1, "02-02-2026"));
        transactions.add(new PharmacyTransaction("TXN203", "3", "MED003", 3, "03-02-2026"));
        transactions.add(new PharmacyTransaction("TXN204", "4", "MED004", 2, "04-02-2026"));
        transactions.add(new PharmacyTransaction("TXN205", "5", "MED005", 1, "05-02-2026"));
        transactions.add(new PharmacyTransaction("TXN206", "6", "MED006", 2, "06-02-2026"));
        transactions.add(new PharmacyTransaction("TXN207", "7", "MED007", 3, "07-02-2026"));
        transactions.add(new PharmacyTransaction("TXN208", "8", "MED008", 1, "08-02-2026"));
        transactions.add(new PharmacyTransaction("TXN209", "9", "MED009", 2, "09-02-2026"));
        transactions.add(new PharmacyTransaction("TXN210", "10", "MED010", 1, "10-02-2026"));
        transactions.add(new PharmacyTransaction("TXN211", "11", "MED011", 3, "11-02-2026"));
        transactions.add(new PharmacyTransaction("TXN212", "12", "MED012", 2, "12-02-2026"));
        transactions.add(new PharmacyTransaction("TXN213", "13", "MED013", 1, "13-02-2026"));
        transactions.add(new PharmacyTransaction("TXN214", "14", "MED014", 2, "14-02-2026"));
        transactions.add(new PharmacyTransaction("TXN215", "15", "MED015", 3, "15-02-2026"));
        transactions.add(new PharmacyTransaction("TXN216", "16", "MED016", 1, "16-02-2026"));
        transactions.add(new PharmacyTransaction("TXN217", "17", "MED017", 2, "17-02-2026"));
        transactions.add(new PharmacyTransaction("TXN218", "18", "MED018", 1, "18-02-2026"));
        transactions.add(new PharmacyTransaction("TXN219", "19", "MED019", 3, "19-02-2026"));
        transactions.add(new PharmacyTransaction("TXN220", "20", "MED020", 2, "20-02-2026"));

        return transactions;
    }

    public static SetAndQueueInterface<Prescription> initializeSamplePrescriptions() {
        SetAndQueueInterface<Prescription> prescriptions = new SetQueueArray<>();

        // Prescription for TRE001 - Hypertension (Patient 1)
        SetAndQueueInterface<PrescribedMedicine> pm1 = new SetQueueArray<>();
        pm1.add(new PrescribedMedicine("PM001", "PRE001", "MED007", "Amlodipine", 30, "1 tablet daily", "Take in the morning", 32.80, 984.00, true));
        pm1.add(new PrescribedMedicine("PM001B", "PRE001", "MED014", "Lisinopril", 30, "1 tablet daily", "Take in the evening", 28.40, 852.00, true));

        // Prescription for TRE002 - Migraine (Patient 3)
        SetAndQueueInterface<PrescribedMedicine> pm2 = new SetQueueArray<>();
        pm2.add(new PrescribedMedicine("PM002", "PRE002", "MED003", "Ibuprofen", 20, "1 tablet every 6 hours as needed", "Take with food for migraine", 12.90, 258.00, true));
        pm2.add(new PrescribedMedicine("PM002B", "PRE002", "MED001", "Paracetamol", 30, "1-2 tablets every 4-6 hours", "For additional pain relief", 8.50, 255.00, true));

        // Prescription for TRE003 - Diabetes Type 2 (Patient 2)
        SetAndQueueInterface<PrescribedMedicine> pm3 = new SetQueueArray<>();
        pm3.add(new PrescribedMedicine("PM003", "PRE003", "MED006", "Metformin", 60, "1 tablet twice daily", "Take with meals", 22.40, 1344.00, true));
        pm3.add(new PrescribedMedicine("PM003B", "PRE003", "MED026", "Glipizide", 30, "1 tablet daily", "Take before breakfast", 26.80, 804.00, true));

        // Prescription for TRE004 - Depression (Patient 6)
        SetAndQueueInterface<PrescribedMedicine> pm4 = new SetQueueArray<>();
        pm4.add(new PrescribedMedicine("PM004", "PRE004", "MED009", "Sertraline", 30, "1 tablet daily", "Take in the morning", 45.60, 1368.00, true));

        // Prescription for TRE005 - Hypothyroidism (Patient 4)
        SetAndQueueInterface<PrescribedMedicine> pm5 = new SetQueueArray<>();
        pm5.add(new PrescribedMedicine("PM005", "PRE005", "MED036", "Levothyroxine", 30, "1 tablet daily", "Take on empty stomach in morning", 18.90, 567.00, true));

        // Prescription for TRE006 - Childhood Asthma (Patient 8)
        SetAndQueueInterface<PrescribedMedicine> pm6 = new SetQueueArray<>();
        pm6.add(new PrescribedMedicine("PM006", "PRE006", "MED008", "Salbutamol", 1, "2 puffs as needed", "Use during asthma attacks", 28.90, 28.90, true));
        pm6.add(new PrescribedMedicine("PM006B", "PRE006", "MED028", "Budesonide", 1, "2 puffs twice daily", "Preventive inhaler, rinse mouth after use", 52.30, 52.30, true));

        // Prescription for TRE007 - Anxiety Disorder (Patient 9)
        SetAndQueueInterface<PrescribedMedicine> pm7 = new SetQueueArray<>();
        pm7.add(new PrescribedMedicine("PM007", "PRE007", "MED038", "Alprazolam", 30, "0.5mg twice daily", "Take as needed for anxiety", 22.80, 684.00, true));
        pm7.add(new PrescribedMedicine("PM007B", "PRE007", "MED015", "Fluoxetine", 30, "1 tablet daily", "Long-term anxiety management", 42.30, 1269.00, true));

        // Prescription for TRE008 - High Cholesterol (Patient 11)
        SetAndQueueInterface<PrescribedMedicine> pm8 = new SetQueueArray<>();
        pm8.add(new PrescribedMedicine("PM008", "PRE008", "MED010", "Atorvastatin", 30, "1 tablet daily", "Take in the evening", 58.90, 1767.00, true));

        // Prescription for TRE009 - Eczema (Patient 12)
        SetAndQueueInterface<PrescribedMedicine> pm9 = new SetQueueArray<>();
        pm9.add(new PrescribedMedicine("PM009", "PRE009", "MED005", "Cetirizine", 30, "1 tablet daily", "For eczema-related itching", 18.20, 546.00, true));
        pm9.add(new PrescribedMedicine("PM009B", "PRE009", "MED033", "Prednisone", 10, "1 tablet daily", "Short-term anti-inflammatory", 15.80, 158.00, true));

        // Prescription for TRE010 - Acute Bronchitis (Patient 13)
        SetAndQueueInterface<PrescribedMedicine> pm10 = new SetQueueArray<>();
        pm10.add(new PrescribedMedicine("PM010", "PRE010", "MED002", "Amoxicillin", 14, "1 capsule three times daily", "Complete full course", 15.80, 221.20, true));
        pm10.add(new PrescribedMedicine("PM010B", "PRE010", "MED008", "Salbutamol", 1, "2 puffs every 4-6 hours", "For breathing relief", 28.90, 28.90, true));

        // Prescription for TRE011 - Cancer Screening (Patient 14) - Preventive care
        SetAndQueueInterface<PrescribedMedicine> pm11 = new SetQueueArray<>();
        pm11.add(new PrescribedMedicine("PM011", "PRE011", "MED001", "Paracetamol", 20, "1-2 tablets as needed", "For post-procedure discomfort", 8.50, 170.00, true));

        // Prescription for TRE012 - Fracture Assessment (Patient 15) - Pain management
        SetAndQueueInterface<PrescribedMedicine> pm12 = new SetQueueArray<>();
        pm12.add(new PrescribedMedicine("PM012", "PRE012", "MED034", "Tramadol", 20, "1 tablet every 6 hours", "For fracture pain", 28.70, 574.00, true));
        pm12.add(new PrescribedMedicine("PM012B", "PRE012", "MED003", "Ibuprofen", 30, "1 tablet twice daily", "Anti-inflammatory", 12.90, 387.00, true));

        // Prescription for TRE013 - Pregnancy Care (Patient 16) - Prenatal vitamins
        SetAndQueueInterface<PrescribedMedicine> pm13 = new SetQueueArray<>();
        pm13.add(new PrescribedMedicine("PM013", "PRE013", "MED001", "Paracetamol", 20, "1-2 tablets as needed", "Safe pain relief during pregnancy", 8.50, 170.00, true));

        // Prescription for TRE014 - Urinary Tract Infection (Patient 17)
        SetAndQueueInterface<PrescribedMedicine> pm14 = new SetQueueArray<>();
        pm14.add(new PrescribedMedicine("PM014", "PRE014", "MED022", "Cephalexin", 14, "1 capsule four times daily", "Complete full antibiotic course", 18.90, 264.60, true));

        // Prescription for TRE015 - Heart Disease Management (Patient 18)
        SetAndQueueInterface<PrescribedMedicine> pm15 = new SetQueueArray<>();
        pm15.add(new PrescribedMedicine("PM015", "PRE015", "MED035", "Warfarin", 30, "1 tablet daily", "Monitor INR regularly", 12.30, 369.00, true));
        pm15.add(new PrescribedMedicine("PM015B", "PRE015", "MED032", "Metoprolol", 30, "1 tablet twice daily", "Heart rate control", 24.60, 738.00, true));

        // Prescription for TRE016 - Gastritis (Patient 19)
        SetAndQueueInterface<PrescribedMedicine> pm16 = new SetQueueArray<>();
        pm16.add(new PrescribedMedicine("PM016", "PRE016", "MED018", "Pantoprazole", 30, "1 tablet daily", "Take before breakfast", 31.20, 936.00, true));
        pm16.add(new PrescribedMedicine("PM016B", "PRE016", "MED024", "Ranitidine", 30, "1 tablet twice daily", "Additional acid control", 19.80, 594.00, true));

        // Prescription for TRE017 - Allergic Rhinitis (Patient 20)
        SetAndQueueInterface<PrescribedMedicine> pm17 = new SetQueueArray<>();
        pm17.add(new PrescribedMedicine("PM017", "PRE017", "MED013", "Loratadine", 30, "1 tablet daily", "For seasonal allergies", 16.80, 504.00, true));
        pm17.add(new PrescribedMedicine("PM017B", "PRE017", "MED025", "Fexofenadine", 30, "1 tablet daily", "Alternative antihistamine", 21.50, 645.00, true));

        // Prescription for TRE018 - Arthritis (Patient 21)
        SetAndQueueInterface<PrescribedMedicine> pm18 = new SetQueueArray<>();
        pm18.add(new PrescribedMedicine("PM018", "PRE018", "MED023", "Diclofenac", 30, "1 tablet twice daily", "Take with food", 14.70, 441.00, true));
        pm18.add(new PrescribedMedicine("PM018B", "PRE018", "MED031", "Naproxen", 30, "1 tablet twice daily", "Additional anti-inflammatory", 11.40, 342.00, true));

        // Prescription for TRE019 - Insomnia (Patient 22)
        SetAndQueueInterface<PrescribedMedicine> pm19 = new SetQueueArray<>();
        pm19.add(new PrescribedMedicine("PM019", "PRE019", "MED040", "Clonazepam", 15, "0.5mg at bedtime", "Short-term sleep aid", 19.60, 294.00, true));

        // Prescription for TRE020 - Chronic Pain (Patient 23)
        SetAndQueueInterface<PrescribedMedicine> pm20 = new SetQueueArray<>();
        pm20.add(new PrescribedMedicine("PM020", "PRE020", "MED039", "Gabapentin", 60, "1 tablet three times daily", "For nerve pain", 31.40, 1884.00, true));
        pm20.add(new PrescribedMedicine("PM020B", "PRE020", "MED034", "Tramadol", 30, "1 tablet every 8 hours", "For breakthrough pain", 28.70, 861.00, true));

        // Additional prescription medicine sets for more treatments
        // Prescription for TRE021 - Hypertension (Patient 24)
        SetAndQueueInterface<PrescribedMedicine> pm21 = new SetQueueArray<>();
        pm21.add(new PrescribedMedicine("PM021", "PRE021", "MED020", "Losartan", 30, "1 tablet daily", "Take in the morning", 29.60, 888.00, true));
        pm21.add(new PrescribedMedicine("PM021B", "PRE021", "MED037", "Hydrochlorothiazide", 30, "1 tablet daily", "Diuretic for blood pressure", 16.50, 495.00, true));

        // Prescription for TRE022 - Asthma (Patient 25)
        SetAndQueueInterface<PrescribedMedicine> pm22 = new SetQueueArray<>();
        pm22.add(new PrescribedMedicine("PM022", "PRE022", "MED008", "Salbutamol", 2, "2 puffs as needed", "Rescue inhaler", 28.90, 57.80, true));
        pm22.add(new PrescribedMedicine("PM022B", "PRE022", "MED017", "Montelukast", 30, "1 tablet daily", "Asthma prevention", 38.90, 1167.00, true));

        // Prescription for TRE023 - Depression (Patient 26)
        SetAndQueueInterface<PrescribedMedicine> pm23 = new SetQueueArray<>();
        pm23.add(new PrescribedMedicine("PM023", "PRE023", "MED029", "Escitalopram", 30, "1 tablet daily", "Take in the morning", 48.90, 1467.00, true));

        // Prescription for TRE024 - Diabetes Type 2 (Patient 27)
        SetAndQueueInterface<PrescribedMedicine> pm24 = new SetQueueArray<>();
        pm24.add(new PrescribedMedicine("PM024", "PRE024", "MED006", "Metformin", 60, "1 tablet twice daily", "Take with meals", 22.40, 1344.00, true));
        pm24.add(new PrescribedMedicine("PM024B", "PRE024", "MED012", "Insulin", 5, "As directed by doctor", "Blood sugar control", 120.00, 600.00, true));

        // Prescription for TRE025 - Peptic Ulcer (Patient 28)
        SetAndQueueInterface<PrescribedMedicine> pm25 = new SetQueueArray<>();
        pm25.add(new PrescribedMedicine("PM025", "PRE025", "MED004", "Omeprazole", 30, "1 tablet twice daily", "Take before meals", 25.60, 768.00, true));
        pm25.add(new PrescribedMedicine("PM025B", "PRE025", "MED002", "Amoxicillin", 14, "1 capsule twice daily", "H. pylori treatment", 15.80, 221.20, true));

        // Create prescription objects that match the treatments
        prescriptions.add(new Prescription("PRE001", "TRE001", "1", "DOC001", "Hypertension", pm1, "10-07-2025", "active", 1836.00, true));
        prescriptions.add(new Prescription("PRE002", "TRE002", "3", "DOC003", "Migraine", pm2, "15-07-2025", "active", 513.00, true));
        prescriptions.add(new Prescription("PRE003", "TRE003", "2", "DOC005", "Diabetes Type 2", pm3, "16-07-2025", "active", 2148.00, true));
        prescriptions.add(new Prescription("PRE004", "TRE004", "6", "DOC006", "Depression", pm4, "18-07-2025", "active", 1368.00, true));
        prescriptions.add(new Prescription("PRE005", "TRE005", "4", "DOC005", "Hypothyroidism", pm5, "19-07-2025", "active", 567.00, true));
        prescriptions.add(new Prescription("PRE006", "TRE006", "8", "DOC002", "Childhood Asthma", pm6, "20-07-2025", "active", 81.20, true));
        prescriptions.add(new Prescription("PRE007", "TRE007", "9", "DOC006", "Anxiety Disorder", pm7, "21-07-2025", "active", 1953.00, true));
        prescriptions.add(new Prescription("PRE008", "TRE008", "11", "DOC001", "High Cholesterol", pm8, "23-07-2025", "active", 1767.00, true));
        prescriptions.add(new Prescription("PRE009", "TRE009", "12", "DOC007", "Eczema", pm9, "24-07-2025", "active", 704.00, true));
        prescriptions.add(new Prescription("PRE010", "TRE010", "13", "DOC008", "Acute Bronchitis", pm10, "25-07-2025", "active", 250.10, true));
        prescriptions.add(new Prescription("PRE011", "TRE011", "14", "DOC009", "Cancer Screening", pm11, "26-07-2025", "active", 170.00, true));
        prescriptions.add(new Prescription("PRE012", "TRE012", "15", "DOC010", "Fracture Assessment", pm12, "27-07-2025", "active", 961.00, true));
        prescriptions.add(new Prescription("PRE013", "TRE013", "16", "DOC011", "Pregnancy Care", pm13, "28-07-2025", "active", 170.00, true));
        prescriptions.add(new Prescription("PRE014", "TRE014", "17", "DOC012", "Urinary Tract Infection", pm14, "29-07-2025", "active", 264.60, true));
        prescriptions.add(new Prescription("PRE015", "TRE015", "18", "DOC001", "Heart Disease Management", pm15, "30-07-2025", "active", 1107.00, true));
        prescriptions.add(new Prescription("PRE016", "TRE016", "19", "DOC013", "Gastritis", pm16, "02-08-2025", "active", 1530.00, true));
        prescriptions.add(new Prescription("PRE017", "TRE017", "20", "DOC006", "Allergic Rhinitis", pm17, "01-08-2025", "active", 1149.00, true));
        prescriptions.add(new Prescription("PRE018", "TRE018", "21", "DOC007", "Arthritis", pm18, "03-08-2025", "active", 783.00, true));
        prescriptions.add(new Prescription("PRE019", "TRE019", "22", "DOC008", "Insomnia", pm19, "04-08-2025", "active", 294.00, true));
        prescriptions.add(new Prescription("PRE020", "TRE020", "23", "DOC009", "Chronic Pain", pm20, "05-08-2025", "active", 2745.00, true));
        prescriptions.add(new Prescription("PRE021", "TRE021", "24", "DOC010", "Hypertension", pm21, "06-08-2025", "active", 1383.00, true));
        prescriptions.add(new Prescription("PRE022", "TRE022", "25", "DOC011", "Asthma", pm22, "07-08-2025", "active", 1224.80, true));
        prescriptions.add(new Prescription("PRE023", "TRE023", "26", "DOC012", "Depression", pm23, "08-08-2025", "active", 1467.00, true));
        prescriptions.add(new Prescription("PRE024", "TRE024", "27", "DOC013", "Diabetes Type 2", pm24, "09-08-2025", "active", 1944.00, true));
        prescriptions.add(new Prescription("PRE025", "TRE025", "28", "DOC013", "Peptic Ulcer", pm25, "11-08-2025", "active", 989.20, true));
        // Additional prescriptions that properly match treatments
        prescriptions.add(new Prescription("PRE026", "TRE026", "29", "DOC006", "Bipolar Disorder", pm23, "12-08-2025", "active", 1467.00, true));
        prescriptions.add(new Prescription("PRE027", "TRE027", "30", "DOC002", "Allergic Asthma", pm22, "13-08-2025", "active", 1224.80, true));
        prescriptions.add(new Prescription("PRE028", "TRE028", "31", "DOC008", "Acute Myocardial Infarction", pm15, "14-08-2025", "active", 1107.00, true));
        prescriptions.add(new Prescription("PRE029", "TRE029", "32", "DOC014", "Crohn's Disease", pm25, "15-08-2025", "active", 989.20, true));
        prescriptions.add(new Prescription("PRE030", "TRE030", "33", "DOC015", "Osteoarthritis", pm18, "16-08-2025", "active", 783.00, true));

        return prescriptions;
    }
}